package com.example.chatassistant.service;

import com.example.chatassistant.dto.ChatRequest;
import com.example.chatassistant.dto.HistoryRequest;
import com.example.chatassistant.dto.ChatRecordsRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;

@Service
public class ChatService {
    
    @Value("${chat.api.base-url:http://localhost:3000}")
    private String apiBaseUrl;
    
    @Value("${chat.api.token:fastgpt-xxxxxx}")
    private String apiToken;
    
    @Value("${chat.api.app-id:default-app-id}")
    private String defaultAppId;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    public ChatService() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 流式聊天接口
     * 处理聊天请求并返回流式响应
     */
    public Flux<String> streamChat(ChatRequest request) {
        // 补充用户信息和token
        if (request.getVariables() == null) {
            request.setVariables(new HashMap<>());
        }

        // 添加默认用户信息
        request.getVariables().putIfAbsent("uid", "default-user-id");
        request.getVariables().putIfAbsent("name", "用户");

        // 调试日志
        System.out.println("=== 发送聊天请求 ===");
        System.out.println("API URL: " + apiBaseUrl + "/api/v1/chat/completions");
        System.out.println("Token: " + apiToken);
        System.out.println("Authorization Header: Bearer " + apiToken);

        return webClient.post()
                .uri(apiBaseUrl + "/api/v1/chat/completions")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiToken)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class)
                .onErrorResume(throwable -> {
                    System.err.println("=== API调用失败 ===");
                    System.err.println("错误信息: " + throwable.getMessage());
                    
                    // 返回错误信息给前端
                    String errorResponse = "data: {\"choices\":[{\"delta\":{\"content\":\"抱歉，服务暂时不可用。可能的原因：\\n1. API密钥配置错误\\n2. 外部服务连接失败\\n3. 请检查网络连接\\n\\n请联系管理员或稍后重试。\"}}]}\n\n";
                    return Flux.just(errorResponse);
                })
                .doOnNext(data -> {
                    System.out.println("=== 收到流式数据 ===");
                    System.out.println("数据长度: " + data.length());
                    System.out.println("数据内容: " + data);

                    // 检查是否包含错误信息
                    if (data.contains("\"code\":514") || data.contains("unAuthApiKey")) {
                        System.err.println("=== API认证失败 ===");
                        System.err.println("检测到API密钥认证失败，请检查配置文件中的token设置");
                    }
                })
                .doOnComplete(() -> {
                    System.out.println("=== 流式数据完成 ===");
                })
                .doOnError(error -> {
                    System.err.println("=== 流式数据错误 ===");
                    System.err.println("错误: " + error.getMessage());
                });
    }
    
    public Flux<String> getHistories(HistoryRequest request) {
        // 补充appId
        request.setAppId(defaultAppId);
        
        return webClient.post()
                .uri(apiBaseUrl + "/api/core/chat/getHistories")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiToken)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class);
    }
    
    public Flux<String> getChatRecords(ChatRecordsRequest request) {
        // 补充appId
        request.setAppId(defaultAppId);
        
        return webClient.post()
                .uri(apiBaseUrl + "/api/core/chat/getPaginationRecords")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiToken)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class);
    }
}
