package com.example.chatassistant.service;

import com.example.chatassistant.dto.ChatRequest;
import com.example.chatassistant.dto.HistoryRequest;
import com.example.chatassistant.dto.ChatRecordsRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;

@Service
public class ChatService {
    
    @Value("${chat.api.base-url:http://localhost:3000}")
    private String apiBaseUrl;
    
    @Value("${chat.api.token:fastgpt-xxxxxx}")
    private String apiToken;
    
    @Value("${chat.api.app-id:default-app-id}")
    private String defaultAppId;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    public ChatService() {
        this.webClient = WebClient.builder().build();
        this.objectMapper = new ObjectMapper();
    }
    
    public Flux<String> streamChat(ChatRequest request) {
        // 补充用户信息和token
        if (request.getVariables() == null) {
            request.setVariables(new HashMap<>());
        }

        // 添加默认用户信息
        request.getVariables().putIfAbsent("uid", "default-user-id");
        request.getVariables().putIfAbsent("name", "用户");

        // 调试日志
        System.out.println("=== 发送聊天请求 ===");
        System.out.println("API URL: " + apiBaseUrl + "/api/v1/chat/completions");
        System.out.println("Token: " + apiToken);
        System.out.println("Authorization Header: Bearer " + apiToken);

        return webClient.post()
                .uri(apiBaseUrl + "/api/v1/chat/completions")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiToken)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class);
    }
    
    public Flux<String> getHistories(HistoryRequest request) {
        // 补充appId
        request.setAppId(defaultAppId);
        
        return webClient.post()
                .uri(apiBaseUrl + "/api/core/chat/getHistories")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiToken)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class);
    }
    
    public Flux<String> getChatRecords(ChatRecordsRequest request) {
        // 补充appId
        request.setAppId(defaultAppId);
        
        return webClient.post()
                .uri(apiBaseUrl + "/api/core/chat/getPaginationRecords")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + apiToken)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class);
    }
}
