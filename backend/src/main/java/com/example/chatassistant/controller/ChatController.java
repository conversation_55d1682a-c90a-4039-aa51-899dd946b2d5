package com.example.chatassistant.controller;

import com.example.chatassistant.dto.ChatRequest;
import com.example.chatassistant.dto.HistoryRequest;
import com.example.chatassistant.dto.ChatRecordsRequest;
import com.example.chatassistant.service.ChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class ChatController {
    
    @Autowired
    private ChatService chatService;
    
    @PostMapping(value = "/chat/completions", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamChat(@RequestBody ChatRequest request) {
        return chatService.streamChat(request);
    }
    
    @PostMapping("/chat/histories")
    public Flux<String> getHistories(@RequestBody HistoryRequest request) {
        return chatService.getHistories(request);
    }
    
    @PostMapping("/chat/records")
    public Flux<String> getChatRecords(@RequestBody ChatRecordsRequest request) {
        return chatService.getChatRecords(request);
    }
    
    @GetMapping("/health")
    public ResponseEntity<java.util.Map<String, String>> health() {
        java.util.Map<String, String> response = new java.util.HashMap<>();
        response.put("status", "UP");
        response.put("service", "chat-assistant");
        response.put("timestamp", java.time.Instant.now().toString());
        return ResponseEntity.ok(response);
    }
}
