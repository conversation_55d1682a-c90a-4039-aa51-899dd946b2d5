package com.example.chatassistant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

public class ChatRequest {
    @JsonProperty("chatId")
    private String chatId;
    
    @JsonProperty("stream")
    private boolean stream = true;
    
    @JsonProperty("detail")
    private boolean detail = false;
    
    @JsonProperty("responseChatItemId")
    private String responseChatItemId;
    
    @JsonProperty("variables")
    private Map<String, Object> variables;
    
    @JsonProperty("messages")
    private List<ChatMessage> messages;

    // Getters and Setters
    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public boolean isStream() {
        return stream;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
    }

    public boolean isDetail() {
        return detail;
    }

    public void setDetail(boolean detail) {
        this.detail = detail;
    }

    public String getResponseChatItemId() {
        return responseChatItemId;
    }

    public void setResponseChatItemId(String responseChatItemId) {
        this.responseChatItemId = responseChatItemId;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public List<ChatMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<ChatMessage> messages) {
        this.messages = messages;
    }
}
