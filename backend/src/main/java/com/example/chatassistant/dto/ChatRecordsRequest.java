package com.example.chatassistant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ChatRecordsRequest {
    @JsonProperty("appId")
    private String appId;
    
    @JsonProperty("chatId")
    private String chatId;
    
    @JsonProperty("offset")
    private int offset = 0;
    
    @JsonProperty("pageSize")
    private int pageSize = 10;
    
    @JsonProperty("loadCustomFeedbacks")
    private boolean loadCustomFeedbacks = true;

    // Getters and Setters
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public boolean isLoadCustomFeedbacks() {
        return loadCustomFeedbacks;
    }

    public void setLoadCustomFeedbacks(boolean loadCustomFeedbacks) {
        this.loadCustomFeedbacks = loadCustomFeedbacks;
    }
}
