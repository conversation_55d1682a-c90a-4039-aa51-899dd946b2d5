package com.example.chatassistant.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class HistoryRequest {
    @JsonProperty("appId")
    private String appId;
    
    @JsonProperty("offset")
    private int offset = 0;
    
    @JsonProperty("pageSize")
    private int pageSize = 20;
    
    @JsonProperty("source")
    private String source = "api";

    // Getters and Setters
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }
}
