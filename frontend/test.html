<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天助手测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e9ecef;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>聊天助手测试页面</h1>
        
        <div class="status" id="status">
            正在初始化聊天助手...
        </div>
        
        <div>
            <button class="btn" onclick="showWidget()">显示聊天助手</button>
            <button class="btn" onclick="hideWidget()">隐藏聊天助手</button>
            <button class="btn" onclick="toggleWidget()">切换显示</button>
            <button class="btn" onclick="newChatTest()">新建会话</button>
            <button class="btn" onclick="sendTestMessage()">发送测试消息</button>
            <button class="btn" onclick="checkStatus()">检查状态</button>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>调试信息：</h3>
            <div id="debug" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-wrap;"></div>
        </div>
    </div>

    <!-- 聊天助手脚本 -->
    <script src="chat-assistant.js"></script>

    <script>
        let chatAssistant = null;

        // 调试函数
        function log(message) {
            const debug = document.getElementById('debug');
            const timestamp = new Date().toLocaleTimeString();
            debug.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status';
            if (type === 'error') status.className += ' error';
            if (type === 'success') status.className += ' success';
        }

        // 页面加载完成后初始化聊天助手
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，初始化聊天助手...');

            try {
                chatAssistant = new ChatAssistant({
                    apiBaseUrl: 'http://localhost:8080/api',
                    mode: 'float',
                    autoShow: false,
                    onReady: function() {
                        log('聊天助手已准备就绪');
                        updateStatus('聊天助手已准备就绪', 'success');
                    },
                    onError: function(error) {
                        log('聊天助手错误: ' + JSON.stringify(error));
                        updateStatus('聊天助手加载失败', 'error');
                    }
                });
            } catch (error) {
                log('初始化聊天助手失败: ' + error.message);
                updateStatus('初始化失败', 'error');
            }
        });

        // 测试函数
        function showWidget() {
            log('尝试显示聊天助手');
            if (chatAssistant) {
                chatAssistant.show();
            } else {
                log('聊天助手未初始化');
                updateStatus('聊天助手未初始化', 'error');
            }
        }

        function hideWidget() {
            log('尝试隐藏聊天助手');
            if (chatAssistant) {
                chatAssistant.hide();
            } else {
                log('聊天助手未初始化');
            }
        }

        function toggleWidget() {
            log('尝试切换聊天助手显示状态');
            if (chatAssistant) {
                if (chatAssistant.isVisible) {
                    chatAssistant.hide();
                } else {
                    chatAssistant.show();
                }
            } else {
                log('聊天助手未初始化');
            }
        }

        function newChatTest() {
            log('尝试新建会话');
            if (chatAssistant) {
                chatAssistant.newChat();
                log('新会话已创建');
            } else {
                log('聊天助手未初始化');
            }
        }

        function sendTestMessage() {
            log('尝试发送测试消息');
            if (chatAssistant) {
                chatAssistant.show();
                setTimeout(() => {
                    if (chatAssistant.inputElement) {
                        chatAssistant.inputElement.value = '这是一条测试消息！请展示你的功能。';
                        chatAssistant.sendMessage();
                    }
                }, 300);
            } else {
                log('聊天助手未初始化');
            }
        }

        function checkStatus() {
            log('检查聊天助手状态');
            log('chatAssistant 对象: ' + (chatAssistant ? '已定义' : '未定义'));
            if (chatAssistant) {
                log('chatAssistant.isVisible: ' + chatAssistant.isVisible);
                log('chatAssistant.container: ' + (chatAssistant.container ? '已创建' : '未创建'));
            }

            const container = document.querySelector('.chat-assistant-container');
            log('聊天容器: ' + (container ? '已创建' : '未创建'));

            if (container) {
                log('容器类名: ' + container.className);
            }
        }

        log('测试页面脚本已加载');
    </script>
</body>
</html>
