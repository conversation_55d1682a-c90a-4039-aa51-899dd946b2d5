<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>聊天助手简单测试</h1>
        
        <div class="info">
            <p>这是一个简单的测试页面，用于验证聊天助手的基本功能。</p>
            <p>聊天助手应该会自动加载在页面右下角。</p>
        </div>
        
        <div>
            <button class="btn" onclick="showChat()">显示聊天助手</button>
            <button class="btn" onclick="hideChat()">隐藏聊天助手</button>
        </div>
        
        <div class="info">
            <h3>使用说明：</h3>
            <ul>
                <li>聊天助手会以浮动窗口的形式出现在右下角</li>
                <li>可以拖拽移动窗口位置</li>
                <li>点击最小化按钮可以收起窗口</li>
                <li>点击关闭按钮可以关闭窗口</li>
            </ul>
        </div>
    </div>

    <script>
        // 配置聊天助手
        window.CHAT_WIDGET_URL = './chat-widget/';
        window.CHAT_WIDGET_AUTO_SHOW = true;  // 自动显示
        window.CHAT_WIDGET_AUTO_INIT = true;  // 自动初始化
        
        function showChat() {
            if (window.ChatWidget) {
                window.ChatWidget.show();
                console.log('显示聊天助手');
            } else {
                console.error('ChatWidget 未定义');
                alert('聊天助手未加载，请刷新页面重试');
            }
        }
        
        function hideChat() {
            if (window.ChatWidget) {
                window.ChatWidget.hide();
                console.log('隐藏聊天助手');
            } else {
                console.error('ChatWidget 未定义');
            }
        }
        
        // 监听聊天助手事件
        document.addEventListener('chatWidgetReady', function(event) {
            console.log('聊天助手已准备就绪:', event.detail);
        });
        
        document.addEventListener('chatWidgetError', function(event) {
            console.error('聊天助手错误:', event.detail);
        });
        
        console.log('页面脚本已加载');
    </script>
    
    <!-- 加载聊天助手 -->
    <script src="./chat-widget/embed.js"></script>
</body>
</html>
