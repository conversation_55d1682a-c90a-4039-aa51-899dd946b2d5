class ChatWidget {
    constructor() {
        this.apiBaseUrl = 'http://localhost:8080/api';
        this.currentChatId = this.generateChatId();
        this.isMinimized = false;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.messages = [];

        this.initializeMarkdown();
        this.initializeElements();
        this.bindEvents();
        this.loadChatHistory();
    }

    initializeMarkdown() {
        // 配置marked选项
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                breaks: true,
                gfm: true,
                highlight: function(code, lang) {
                    if (typeof Prism !== 'undefined' && lang && Prism.languages[lang]) {
                        return Prism.highlight(code, Prism.languages[lang], lang);
                    }
                    return code;
                }
            });
        }
    }

    initializeElements() {
        this.widget = document.getElementById('chat-widget');
        this.header = document.getElementById('chat-header');
        this.messagesContainer = document.getElementById('chat-messages');
        this.input = document.getElementById('chat-input');
        this.sendButton = document.getElementById('send-button');
        this.floatButton = document.getElementById('chat-float-button');
        this.historyPanel = document.getElementById('history-panel');
        this.historyList = document.getElementById('history-list');
        
        // 按钮元素
        this.btnMinimize = document.getElementById('btn-minimize');
        this.btnClose = document.getElementById('btn-close');
        this.btnHistory = document.getElementById('btn-history');
        this.btnCloseHistory = document.getElementById('btn-close-history');
    }

    bindEvents() {
        // 发送消息事件
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 自动调整输入框高度
        this.input.addEventListener('input', () => this.adjustInputHeight());

        // 窗口控制事件
        this.btnMinimize.addEventListener('click', () => this.minimize());
        this.btnClose.addEventListener('click', () => this.close());
        this.btnHistory.addEventListener('click', () => this.showHistory());
        this.btnCloseHistory.addEventListener('click', () => this.hideHistory());
        
        // 浮动按钮事件
        this.floatButton.addEventListener('click', () => this.restore());

        // 拖拽事件
        this.header.addEventListener('mousedown', (e) => this.startDrag(e));
        document.addEventListener('mousemove', (e) => this.drag(e));
        document.addEventListener('mouseup', () => this.endDrag());

        // 防止拖拽时选中文本
        this.header.addEventListener('selectstart', (e) => e.preventDefault());
    }

    generateChatId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    adjustInputHeight() {
        this.input.style.height = 'auto';
        this.input.style.height = Math.min(this.input.scrollHeight, 100) + 'px';
    }

    // 拖拽功能
    startDrag(e) {
        this.isDragging = true;
        const rect = this.widget.getBoundingClientRect();
        this.dragOffset.x = e.clientX - rect.left;
        this.dragOffset.y = e.clientY - rect.top;
        this.widget.style.transition = 'none';
    }

    drag(e) {
        if (!this.isDragging) return;
        
        const x = e.clientX - this.dragOffset.x;
        const y = e.clientY - this.dragOffset.y;
        
        // 限制拖拽范围
        const maxX = window.innerWidth - this.widget.offsetWidth;
        const maxY = window.innerHeight - this.widget.offsetHeight;
        
        const constrainedX = Math.max(0, Math.min(x, maxX));
        const constrainedY = Math.max(0, Math.min(y, maxY));
        
        this.widget.style.left = constrainedX + 'px';
        this.widget.style.top = constrainedY + 'px';
        this.widget.style.right = 'auto';
        this.widget.style.bottom = 'auto';
    }

    endDrag() {
        if (this.isDragging) {
            this.isDragging = false;
            this.widget.style.transition = '';
        }
    }

    // 窗口控制
    minimize() {
        this.isMinimized = true;
        this.widget.style.display = 'none';
        this.floatButton.style.display = 'flex';
    }

    restore() {
        this.isMinimized = false;
        this.widget.style.display = 'flex';
        this.floatButton.style.display = 'none';
    }

    close() {
        this.widget.style.display = 'none';
        this.floatButton.style.display = 'none';
    }

    // 历史记录
    showHistory() {
        this.historyPanel.style.display = 'flex';
        this.loadHistories();
    }

    hideHistory() {
        this.historyPanel.style.display = 'none';
    }

    // 消息处理
    async sendMessage() {
        const message = this.input.value.trim();
        if (!message) return;

        // 添加用户消息
        this.addMessage('user', message);
        this.input.value = '';
        this.adjustInputHeight();

        // 禁用发送按钮
        this.sendButton.disabled = true;

        // 显示打字指示器
        this.showTypingIndicator();

        try {
            await this.streamChatResponse(message);
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('bot', '抱歉，发生了错误，请稍后再试。');
        } finally {
            this.hideTypingIndicator();
            this.sendButton.disabled = false;
        }
    }

    addMessage(role, content, timestamp = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        const messageText = document.createElement('div');
        messageText.className = 'message-text';

        // 根据角色决定是否渲染Markdown
        if (role === 'bot' && typeof marked !== 'undefined') {
            messageText.innerHTML = marked.parse(content || '');
        } else {
            messageText.textContent = content;
        }

        messageContent.appendChild(messageText);

        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = timestamp || this.formatTime(new Date());

        messageDiv.appendChild(messageContent);
        messageDiv.appendChild(messageTime);

        // 移除欢迎消息
        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage && this.messages.length === 0) {
            welcomeMessage.remove();
        }

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();

        // 保存消息到内存
        this.messages.push({ role, content, timestamp: timestamp || new Date().toISOString() });

        return messageDiv;
    }

    addMessageWithReasoning(role, content, reasoning, timestamp = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;

        // 推理过程区域（可折叠）
        if (role === 'bot') {
            const reasoningSection = document.createElement('div');
            reasoningSection.className = 'reasoning-section';

            const reasoningHeader = document.createElement('div');
            reasoningHeader.className = 'reasoning-header';
            reasoningHeader.innerHTML = `
                <span class="reasoning-icon">🧠</span>
                <span class="reasoning-title">思考过程</span>
                <span class="reasoning-toggle">▲</span>
            `;

            const reasoningContentDiv = document.createElement('div');
            reasoningContentDiv.className = 'reasoning-content';
            reasoningContentDiv.textContent = reasoning;

            reasoningSection.appendChild(reasoningHeader);
            reasoningSection.appendChild(reasoningContentDiv);

            // 添加折叠/展开功能
            reasoningHeader.addEventListener('click', () => {
                const isCollapsed = reasoningContentDiv.style.display === 'none';
                reasoningContentDiv.style.display = isCollapsed ? 'block' : 'none';
                reasoningHeader.querySelector('.reasoning-toggle').textContent = isCollapsed ? '▲' : '▼';
            });

            messageDiv.appendChild(reasoningSection);
        }

        // 主要回复内容
        const mainContent = document.createElement('div');
        mainContent.className = 'main-content message-content';

        const messageText = document.createElement('div');
        messageText.className = 'message-text';

        // 渲染Markdown内容
        if (typeof marked !== 'undefined') {
            messageText.innerHTML = marked.parse(content || '');
        } else {
            messageText.textContent = content;
        }

        mainContent.appendChild(messageText);

        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = timestamp || this.formatTime(new Date());

        messageDiv.appendChild(mainContent);
        messageDiv.appendChild(messageTime);

        // 移除欢迎消息
        const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage && this.messages.length === 0) {
            welcomeMessage.remove();
        }

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();

        // 保存消息到内存
        this.messages.push({
            role,
            content,
            reasoning: reasoning,
            timestamp: timestamp || new Date().toISOString()
        });

        return messageDiv;
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message';
        typingDiv.id = 'typing-indicator';

        const typingContent = document.createElement('div');
        typingContent.className = 'typing-indicator';

        const typingDots = document.createElement('div');
        typingDots.className = 'typing-dots';

        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'typing-dot';
            typingDots.appendChild(dot);
        }

        typingContent.appendChild(typingDots);
        typingDiv.appendChild(typingContent);

        this.messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    async streamChatResponse(userMessage) {
        const requestBody = {
            chatId: this.currentChatId,
            stream: true,
            detail: false,
            responseChatItemId: 'response_' + Date.now(),
            variables: {
                uid: 'user_' + Date.now(),
                name: '用户'
            },
            messages: [
                ...this.messages.slice(-10), // 只发送最近10条消息作为上下文
                { role: 'user', content: userMessage }
            ]
        };

        const response = await fetch(`${this.apiBaseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let botMessageDiv = null;
        let botMessageContent = '';
        let reasoningContent = '';
        let reasoningDiv = null;
        let hasReasoning = false;

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data:')) {
                        // 处理可能的双重data:前缀
                        let data = line.slice(5).trim(); // 移除 'data:'
                        if (data.startsWith('data:')) {
                            data = data.slice(5).trim(); // 再次移除 'data:'
                        }

                        if (data === '[DONE]') {
                            return;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            const delta = parsed.choices?.[0]?.delta;

                            // 处理推理过程内容
                            if (delta?.reasoning_content) {
                                hasReasoning = true;
                                if (!botMessageDiv) {
                                    this.hideTypingIndicator();
                                    botMessageDiv = this.addMessageWithReasoning('bot', '', '');
                                    botMessageContent = '';
                                    reasoningContent = '';
                                    reasoningDiv = botMessageDiv.querySelector('.reasoning-content');
                                }

                                reasoningContent += delta.reasoning_content;
                                if (reasoningDiv) {
                                    reasoningDiv.textContent = reasoningContent;
                                }
                                this.scrollToBottom();
                            }

                            // 处理实际的回复内容（用户可见）
                            if (delta?.content) {
                                if (!botMessageDiv) {
                                    this.hideTypingIndicator();
                                    // 根据是否有推理内容决定使用哪种消息类型
                                    if (hasReasoning) {
                                        botMessageDiv = this.addMessageWithReasoning('bot', '', '');
                                        reasoningDiv = botMessageDiv.querySelector('.reasoning-content');
                                    } else {
                                        botMessageDiv = this.addMessage('bot', '');
                                    }
                                    botMessageContent = '';
                                    reasoningContent = '';
                                }

                                botMessageContent += delta.content;
                                // 根据消息类型选择正确的文本元素
                                const messageText = hasReasoning ?
                                    botMessageDiv.querySelector('.main-content .message-text') :
                                    botMessageDiv.querySelector('.message-text');
                                if (messageText) {
                                    // 实时渲染Markdown
                                    if (typeof marked !== 'undefined') {
                                        messageText.innerHTML = marked.parse(botMessageContent || '');
                                    } else {
                                        messageText.textContent = botMessageContent;
                                    }
                                }
                                this.scrollToBottom();
                            }
                        } catch (e) {
                            console.log('解析流式数据失败:', e, '数据:', data);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    }

    // 历史记录功能
    async loadHistories() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/chat/histories`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    offset: 0,
                    pageSize: 20,
                    source: 'api'
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.renderHistories(data.data?.list || []);
            }
        } catch (error) {
            console.error('加载历史记录失败:', error);
        }
    }

    renderHistories(histories) {
        this.historyList.innerHTML = '';

        if (histories.length === 0) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'history-empty';
            emptyDiv.textContent = '暂无历史记录';
            emptyDiv.style.textAlign = 'center';
            emptyDiv.style.color = '#666';
            emptyDiv.style.padding = '20px';
            this.historyList.appendChild(emptyDiv);
            return;
        }

        histories.forEach(history => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';

            const title = document.createElement('div');
            title.className = 'history-title';
            title.textContent = history.customTitle || history.title || '未命名对话';

            const time = document.createElement('div');
            time.className = 'history-time';
            time.textContent = this.formatTime(new Date(history.updateTime));

            historyItem.appendChild(title);
            historyItem.appendChild(time);

            historyItem.addEventListener('click', () => {
                this.loadChatRecords(history.chatId);
                this.hideHistory();
            });

            this.historyList.appendChild(historyItem);
        });
    }

    async loadChatRecords(chatId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/chat/records`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chatId: chatId,
                    offset: 0,
                    pageSize: 50,
                    loadCustomFeedbacks: true
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.renderChatRecords(data.data?.list || []);
                this.currentChatId = chatId;
            }
        } catch (error) {
            console.error('加载对话记录失败:', error);
        }
    }

    renderChatRecords(records) {
        // 清空当前消息
        this.messagesContainer.innerHTML = '';
        this.messages = [];

        records.forEach(record => {
            const role = record.obj === 'Human' ? 'user' : 'bot';
            const content = record.value?.[0]?.text?.content || '';
            if (content) {
                this.addMessage(role, content);
            }
        });

        if (records.length === 0) {
            // 显示欢迎消息
            const welcomeDiv = document.createElement('div');
            welcomeDiv.className = 'welcome-message';
            welcomeDiv.innerHTML = `
                <div class="message bot-message">
                    <div class="message-content">
                        <p>您好！我是您的智能助手，有什么可以帮助您的吗？</p>
                    </div>
                </div>
            `;
            this.messagesContainer.appendChild(welcomeDiv);
        }
    }

    loadChatHistory() {
        // 从localStorage加载当前会话的消息
        const savedMessages = localStorage.getItem(`chat_messages_${this.currentChatId}`);
        if (savedMessages) {
            try {
                this.messages = JSON.parse(savedMessages);
                this.renderSavedMessages();
            } catch (error) {
                console.error('加载本地消息失败:', error);
            }
        }
    }

    renderSavedMessages() {
        if (this.messages.length > 0) {
            // 移除欢迎消息
            const welcomeMessage = this.messagesContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            this.messages.forEach(msg => {
                this.addMessage(msg.role, msg.content, this.formatTime(new Date(msg.timestamp)));
            });
        }
    }

    saveChatHistory() {
        // 保存当前会话消息到localStorage
        localStorage.setItem(`chat_messages_${this.currentChatId}`, JSON.stringify(this.messages));
    }

    // 工具函数
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
}

// 初始化聊天组件
document.addEventListener('DOMContentLoaded', () => {
    window.chatWidget = new ChatWidget();

    // 定期保存聊天历史
    setInterval(() => {
        if (window.chatWidget) {
            window.chatWidget.saveChatHistory();
        }
    }, 30000); // 每30秒保存一次

    // 页面卸载时保存
    window.addEventListener('beforeunload', () => {
        if (window.chatWidget) {
            window.chatWidget.saveChatHistory();
        }
    });
});
