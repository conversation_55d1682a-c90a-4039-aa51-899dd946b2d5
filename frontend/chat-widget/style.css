/* 聊天窗口主容器 */
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    z-index: 10000;
    border: 1px solid #e1e5e9;
    overflow: hidden;
}

/* 聊天窗口头部 */
.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    user-select: none;
}

.chat-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
}

.chat-icon {
    margin-right: 8px;
    font-size: 16px;
}

.chat-controls {
    display: flex;
    gap: 4px;
}

.chat-controls button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.chat-controls button:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #f8f9fa;
}

.message {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
}

.user-message {
    align-items: flex-end;
}

.bot-message {
    align-items: flex-start;
}

.message-content {
    max-width: 80%;
    padding: 10px 14px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message .message-content {
    background: #007bff;
    color: white;
    border-bottom-right-radius: 4px;
}

.bot-message .message-content {
    background: white;
    color: #333;
    border: 1px solid #e1e5e9;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 11px;
    color: #666;
    margin-top: 4px;
    padding: 0 4px;
}

/* 推理过程样式 */
.reasoning-section {
    max-width: 80%;
    margin-bottom: 8px;
    border: 1px solid #E5E5EA;
    border-radius: 12px;
    background: #FAFAFA;
    overflow: hidden;
}

.reasoning-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: #F0F0F5;
    cursor: pointer;
    user-select: none;
    border-bottom: 1px solid #E5E5EA;
}

.reasoning-header:hover {
    background: #E8E8ED;
}

.reasoning-icon {
    margin-right: 6px;
    font-size: 14px;
}

.reasoning-title {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    color: #3C3C43;
}

.reasoning-toggle {
    font-size: 12px;
    color: #8E8E93;
    transition: transform 0.2s ease;
}

.reasoning-content {
    padding: 10px 12px;
    font-size: 12px;
    line-height: 1.4;
    color: #666;
    white-space: pre-wrap;
    background: #FAFAFA;
    border-top: 1px solid #F0F0F0;
}

.main-content {
    margin-top: 0;
}

/* 历史记录面板 */
.history-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    z-index: 10;
    display: flex;
    flex-direction: column;
}

.history-header {
    background: #f8f9fa;
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.btn-close-history {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.history-item {
    padding: 12px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.history-item:hover {
    background: #f8f9fa;
}

.history-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

.history-time {
    font-size: 12px;
    color: #666;
}

/* 输入区域 */
.chat-input-area {
    border-top: 1px solid #e1e5e9;
    padding: 12px 16px;
    background: white;
}

.chat-input-container {
    display: flex;
    align-items: flex-end;
    gap: 8px;
}

.chat-input {
    flex: 1;
    border: 1px solid #e1e5e9;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    resize: none;
    outline: none;
    max-height: 100px;
    min-height: 20px;
    font-family: inherit;
}

.chat-input:focus {
    border-color: #007bff;
}

.send-button {
    background: #007bff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    flex-shrink: 0;
}

.send-button:hover {
    background: #0056b3;
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.send-icon {
    font-size: 14px;
}

/* 浮动按钮 */
.chat-float-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    transition: transform 0.2s;
}

.chat-float-button:hover {
    transform: scale(1.05);
}

.float-icon {
    font-size: 24px;
    color: white;
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* 加载动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 10px 14px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    max-width: 80%;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: #666;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar,
.history-list::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.history-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb,
.history-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.history-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
