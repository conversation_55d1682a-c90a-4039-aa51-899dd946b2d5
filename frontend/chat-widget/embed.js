(function() {
    'use strict';
    
    // 配置选项
    const CONFIG = {
        // 聊天助手的URL，可以通过参数配置
        widgetUrl: window.CHAT_WIDGET_URL || 'http://localhost:8080/chat-widget/',
        // 是否自动显示
        autoShow: window.CHAT_WIDGET_AUTO_SHOW !== false,
        // 初始位置
        position: window.CHAT_WIDGET_POSITION || 'bottom-right',
        // 主题色
        theme: window.CHAT_WIDGET_THEME || 'default'
    };

    class ChatWidgetEmbed {
        constructor() {
            this.iframe = null;
            this.isLoaded = false;
            this.messageQueue = [];
            
            this.init();
        }

        init() {
            // 检查是否已经初始化
            if (document.getElementById('chat-widget-iframe')) {
                console.warn('Chat widget already initialized');
                return;
            }

            this.createIframe();
            this.setupMessageHandling();
            
            if (CONFIG.autoShow) {
                this.show();
            }
        }

        createIframe() {
            // 创建iframe容器
            const container = document.createElement('div');
            container.id = 'chat-widget-container';
            container.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 2147483647;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;

            // 创建iframe
            this.iframe = document.createElement('iframe');
            this.iframe.id = 'chat-widget-iframe';
            this.iframe.src = CONFIG.widgetUrl + 'index.html';
            this.iframe.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                border: none;
                background: transparent;
                pointer-events: auto;
            `;

            // 设置iframe属性
            this.iframe.setAttribute('allowtransparency', 'true');
            this.iframe.setAttribute('scrolling', 'no');
            this.iframe.setAttribute('frameborder', '0');

            container.appendChild(this.iframe);
            document.body.appendChild(container);

            // 监听iframe加载完成
            this.iframe.onload = () => {
                this.isLoaded = true;
                this.processMessageQueue();
                this.sendMessage('init', { config: CONFIG });
            };
        }

        setupMessageHandling() {
            // 监听来自iframe的消息
            window.addEventListener('message', (event) => {
                // 验证消息来源
                if (event.source !== this.iframe.contentWindow) {
                    return;
                }

                const { type, data } = event.data;

                switch (type) {
                    case 'widget-ready':
                        this.onWidgetReady(data);
                        break;
                    case 'widget-resize':
                        this.onWidgetResize(data);
                        break;
                    case 'widget-minimize':
                        this.onWidgetMinimize();
                        break;
                    case 'widget-close':
                        this.onWidgetClose();
                        break;
                    case 'widget-error':
                        this.onWidgetError(data);
                        break;
                }
            });
        }

        sendMessage(type, data = {}) {
            const message = { type, data };
            
            if (this.isLoaded && this.iframe.contentWindow) {
                this.iframe.contentWindow.postMessage(message, '*');
            } else {
                // 如果iframe还没加载完成，将消息加入队列
                this.messageQueue.push(message);
            }
        }

        processMessageQueue() {
            while (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                this.iframe.contentWindow.postMessage(message, '*');
            }
        }

        // 事件处理
        onWidgetReady(data) {
            console.log('Chat widget ready:', data);
            
            // 触发自定义事件
            const event = new CustomEvent('chatWidgetReady', { detail: data });
            document.dispatchEvent(event);
        }

        onWidgetResize(data) {
            // 处理窗口大小变化
            if (data.width && data.height) {
                // 可以根据需要调整iframe大小
            }
        }

        onWidgetMinimize() {
            // 处理最小化事件
            const event = new CustomEvent('chatWidgetMinimize');
            document.dispatchEvent(event);
        }

        onWidgetClose() {
            // 处理关闭事件
            const event = new CustomEvent('chatWidgetClose');
            document.dispatchEvent(event);
        }

        onWidgetError(data) {
            console.error('Chat widget error:', data);
            
            const event = new CustomEvent('chatWidgetError', { detail: data });
            document.dispatchEvent(event);
        }

        // 公共API
        show() {
            if (this.iframe) {
                const container = document.getElementById('chat-widget-container');
                if (container) {
                    container.style.display = 'block';
                }
                this.sendMessage('show');
            }
        }

        hide() {
            if (this.iframe) {
                const container = document.getElementById('chat-widget-container');
                if (container) {
                    container.style.display = 'none';
                }
                this.sendMessage('hide');
            }
        }

        toggle() {
            const container = document.getElementById('chat-widget-container');
            if (container && container.style.display === 'none') {
                this.show();
            } else {
                this.hide();
            }
        }

        sendChatMessage(message) {
            this.sendMessage('send-message', { message });
        }

        setConfig(config) {
            this.sendMessage('set-config', config);
        }

        destroy() {
            const container = document.getElementById('chat-widget-container');
            if (container) {
                container.remove();
            }
            this.iframe = null;
            this.isLoaded = false;
            this.messageQueue = [];
        }
    }

    // 全局API
    window.ChatWidget = {
        instance: null,
        
        init: function(config = {}) {
            if (this.instance) {
                console.warn('Chat widget already initialized');
                return this.instance;
            }
            
            // 合并配置
            Object.assign(CONFIG, config);
            
            this.instance = new ChatWidgetEmbed();
            return this.instance;
        },
        
        show: function() {
            if (this.instance) {
                this.instance.show();
            }
        },
        
        hide: function() {
            if (this.instance) {
                this.instance.hide();
            }
        },
        
        toggle: function() {
            if (this.instance) {
                this.instance.toggle();
            }
        },
        
        sendMessage: function(message) {
            if (this.instance) {
                this.instance.sendChatMessage(message);
            }
        },
        
        setConfig: function(config) {
            if (this.instance) {
                this.instance.setConfig(config);
            }
        },
        
        destroy: function() {
            if (this.instance) {
                this.instance.destroy();
                this.instance = null;
            }
        }
    };

    // 自动初始化（如果页面加载完成）
    function initWidget() {
        if (window.CHAT_WIDGET_AUTO_INIT !== false) {
            console.log('Auto-initializing chat widget...');
            window.ChatWidget.init();
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWidget);
    } else {
        // 延迟一点时间确保所有配置都已加载
        setTimeout(initWidget, 100);
    }

})();
