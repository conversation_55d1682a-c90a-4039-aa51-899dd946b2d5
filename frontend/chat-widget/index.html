<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天助手</title>
    <link rel="stylesheet" href="style.css">
    <!-- Markdown解析库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- 代码高亮库 -->
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css">
</head>
<body>
    <div id="chat-widget" class="chat-widget">
        <!-- 聊天窗口头部 -->
        <div class="chat-header" id="chat-header">
            <div class="chat-title">
                <span class="chat-icon">💬</span>
                <span>智能助手</span>
            </div>
            <div class="chat-controls">
                <button class="btn-history" id="btn-history" title="历史记录">📋</button>
                <button class="btn-minimize" id="btn-minimize" title="最小化">−</button>
                <button class="btn-close" id="btn-close" title="关闭">×</button>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" id="chat-messages">
            <div class="welcome-message">
                <div class="message bot-message">
                    <div class="message-content">
                        <p>您好！我是您的智能助手，有什么可以帮助您的吗？</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录面板 -->
        <div class="history-panel" id="history-panel" style="display: none;">
            <div class="history-header">
                <h3>历史对话</h3>
                <button class="btn-close-history" id="btn-close-history">×</button>
            </div>
            <div class="history-list" id="history-list">
                <!-- 历史记录将在这里动态加载 -->
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
            <div class="chat-input-container">
                <textarea
                    id="chat-input"
                    class="chat-input"
                    placeholder="输入您的问题..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="send-button">
                    <span class="send-icon">➤</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 最小化状态的浮动按钮 -->
    <div id="chat-float-button" class="chat-float-button" style="display: none;">
        <span class="float-icon">💬</span>
        <span class="notification-badge" id="notification-badge" style="display: none;">1</span>
    </div>

    <script src="chat-widget.js"></script>
</body>
</html>