<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一行代码嵌入聊天助手</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .code-block {
            background: rgba(0,0,0,0.2);
            border-radius: 12px;
            padding: 30px;
            margin: 40px 0;
            font-family: 'Monaco', '<PERSON>lo', 'Ubuntu Mono', monospace;
            font-size: 16px;
            text-align: left;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .highlight {
            color: #ffd700;
            font-weight: bold;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .demo-section {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 30px;
            margin: 40px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }
        
        .footer {
            margin-top: 60px;
            opacity: 0.7;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 智能聊天助手</h1>
        <p class="subtitle">一行代码，即可为您的网站添加强大的AI聊天功能</p>
        
        <div class="code-block">
            <div class="highlight">// 只需要这一行代码！</div>
            &lt;script src="<span class="highlight">chat-assistant.js</span>"&gt;&lt;/script&gt;<br>
            &lt;script&gt;<span class="highlight">new ChatAssistant()</span>;&lt;/script&gt;
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">美观界面</div>
                <div class="feature-desc">现代化设计，支持自定义主题和样式</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">响应式设计</div>
                <div class="feature-desc">完美适配桌面端和移动端设备</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🧠</div>
                <div class="feature-title">推理过程</div>
                <div class="feature-desc">显示AI的思考过程，增强用户信任</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📝</div>
                <div class="feature-title">Markdown支持</div>
                <div class="feature-desc">自动渲染代码块、表格、列表等格式</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <div class="feature-title">灵活配置</div>
                <div class="feature-desc">支持多种嵌入模式和丰富的配置选项</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">实时流式</div>
                <div class="feature-desc">流式响应，实时显示AI回复内容</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🚀 立即体验</h2>
            <p>点击下方按钮体验不同的嵌入模式：</p>
            
            <button class="btn" onclick="startFloatDemo()">🎈 浮窗模式</button>
            <button class="btn" onclick="startFullscreenDemo()">🖥️ 全屏模式</button>
            <button class="btn" onclick="openDetailDemo()">📋 详细演示</button>
        </div>
        
        <div class="demo-section">
            <h2>📖 使用方法</h2>
            <div style="text-align: left; max-width: 600px; margin: 0 auto;">
                <h3>1. 基础使用</h3>
                <div class="code-block" style="font-size: 14px;">
&lt;script src="chat-assistant.js"&gt;&lt;/script&gt;<br>
&lt;script&gt;<br>
&nbsp;&nbsp;new ChatAssistant({<br>
&nbsp;&nbsp;&nbsp;&nbsp;apiBaseUrl: 'http://localhost:8080/api'<br>
&nbsp;&nbsp;});<br>
&lt;/script&gt;
                </div>
                
                <h3>2. 内嵌模式</h3>
                <div class="code-block" style="font-size: 14px;">
&lt;div id="chat-container"&gt;&lt;/div&gt;<br>
&lt;script&gt;<br>
&nbsp;&nbsp;new ChatAssistant({<br>
&nbsp;&nbsp;&nbsp;&nbsp;mode: 'inline',<br>
&nbsp;&nbsp;&nbsp;&nbsp;container: '#chat-container'<br>
&nbsp;&nbsp;});<br>
&lt;/script&gt;
                </div>
                
                <h3>3. 自定义配置</h3>
                <div class="code-block" style="font-size: 14px;">
new ChatAssistant({<br>
&nbsp;&nbsp;mode: 'float',<br>
&nbsp;&nbsp;theme: {<br>
&nbsp;&nbsp;&nbsp;&nbsp;primaryColor: '#ff6b6b'<br>
&nbsp;&nbsp;},<br>
&nbsp;&nbsp;texts: {<br>
&nbsp;&nbsp;&nbsp;&nbsp;title: '我的助手'<br>
&nbsp;&nbsp;},<br>
&nbsp;&nbsp;features: {<br>
&nbsp;&nbsp;&nbsp;&nbsp;markdown: true,<br>
&nbsp;&nbsp;&nbsp;&nbsp;reasoning: true<br>
&nbsp;&nbsp;}<br>
});
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>💡 提示：确保后端服务正在运行，API地址配置正确</p>
            <p>🔗 项目地址：<a href="https://git.haipi8.top:1001/zhijg/assistant_demo.git" style="color: #ffd700;">https://git.haipi8.top:1001/zhijg/assistant_demo.git</a></p>
        </div>
    </div>

    <!-- 引入聊天助手 -->
    <script src="chat-assistant.js"></script>
    
    <script>
        let currentAssistant = null;
        
        function startFloatDemo() {
            if (currentAssistant) {
                currentAssistant.close();
                // 清理DOM
                const container = document.querySelector('.chat-assistant-container');
                const trigger = document.querySelector('.chat-assistant-trigger');
                if (container) container.remove();
                if (trigger) trigger.remove();
            }
            
            currentAssistant = new ChatAssistant({
                mode: 'float',
                apiBaseUrl: 'http://localhost:8080/api',
                theme: {
                    primaryColor: '#667eea'
                },
                callbacks: {
                    onOpen: () => {
                        console.log('聊天助手已打开');
                        // 自动发送欢迎消息
                        setTimeout(() => {
                            currentAssistant.sendUserMessage('你好！请介绍一下你的功能，并展示一些Markdown格式的内容。');
                        }, 1000);
                    }
                }
            });
        }
        
        function startFullscreenDemo() {
            currentAssistant = new ChatAssistant({
                mode: 'fullscreen',
                apiBaseUrl: 'http://localhost:8080/api',
                theme: {
                    primaryColor: '#764ba2'
                },
                callbacks: {
                    onOpen: () => {
                        // 自动发送欢迎消息
                        setTimeout(() => {
                            currentAssistant.sendUserMessage('请展示你的Markdown渲染能力，包括代码块、表格、列表等。');
                        }, 1000);
                    }
                }
            });
        }
        
        function openDetailDemo() {
            window.open('embed-demo.html', '_blank');
        }
        
        // 页面加载完成后自动启动浮窗演示
        window.addEventListener('load', () => {
            setTimeout(() => {
                startFloatDemo();
            }, 2000);
        });
    </script>
</body>
</html>
