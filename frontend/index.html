<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能聊天助手演示</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>智能助手演示</h1>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link">首页</a>
                <a href="#features" class="nav-link">功能</a>
                <a href="#demo" class="nav-link">演示</a>
                <a href="#contact" class="nav-link">联系</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 首页部分 -->
        <section id="home" class="hero-section">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">智能聊天助手</h1>
                    <p class="hero-subtitle">体验下一代AI驱动的客户服务解决方案</p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary" onclick="showChatWidget()">开始对话</button>
                        <button class="btn btn-secondary" onclick="toggleChatWidget()">切换助手</button>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="chat-preview">
                        <div class="chat-bubble">
                            <p>👋 您好！我是您的智能助手</p>
                        </div>
                        <div class="chat-bubble user">
                            <p>你能帮我做什么？</p>
                        </div>
                        <div class="chat-bubble">
                            <p>我可以回答问题、提供建议、协助解决问题等！</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能部分 -->
        <section id="features" class="features-section">
            <div class="container">
                <h2 class="section-title">核心功能</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">💬</div>
                        <h3>智能对话</h3>
                        <p>基于先进的AI技术，提供自然流畅的对话体验</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3>响应式设计</h3>
                        <p>适配各种设备和屏幕尺寸，随时随地使用</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h3>易于集成</h3>
                        <p>简单的JavaScript代码即可集成到任何网站</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3>历史记录</h3>
                        <p>保存对话历史，支持查看和继续之前的对话</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎨</div>
                        <h3>可定制化</h3>
                        <p>支持自定义主题、颜色和样式</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>实时流式</h3>
                        <p>支持流式响应，实时显示AI回复内容</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 演示部分 -->
        <section id="demo" class="demo-section">
            <div class="container">
                <h2 class="section-title">在线演示</h2>
                <div class="demo-content">
                    <div class="demo-description">
                        <h3>体验聊天助手</h3>
                        <p>聊天助手会自动出现在页面右下角。您可以：</p>
                        <ul>
                            <li>询问任何问题</li>
                            <li>拖拽聊天窗口到任意位置</li>
                            <li>查看历史对话记录</li>
                            <li>最小化或关闭聊天窗口</li>
                        </ul>
                        <div class="demo-controls">
                            <button class="btn btn-primary" onclick="showChatWidget()">显示助手</button>
                            <button class="btn btn-outline" onclick="hideChatWidget()">隐藏助手</button>
                            <button class="btn btn-outline" onclick="sendDemoMessage()">发送演示消息</button>
                        </div>
                    </div>
                    <div class="demo-code">
                        <h4>集成代码</h4>
                        <pre><code>&lt;!-- 在页面中添加以下代码 --&gt;
&lt;script src="chat-widget/embed.js"&gt;&lt;/script&gt;
&lt;script&gt;
  // 初始化聊天助手
  ChatWidget.init({
    widgetUrl: './chat-widget/',
    autoShow: true,
    position: 'bottom-right'
  });
&lt;/script&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系部分 -->
        <section id="contact" class="contact-section">
            <div class="container">
                <h2 class="section-title">联系我们</h2>
                <div class="contact-content">
                    <div class="contact-info">
                        <h3>获取更多信息</h3>
                        <p>如果您对我们的聊天助手感兴趣，欢迎联系我们了解更多详情。</p>
                        <div class="contact-details">
                            <div class="contact-item">
                                <strong>邮箱：</strong> <EMAIL>
                            </div>
                            <div class="contact-item">
                                <strong>电话：</strong> +86 400-123-4567
                            </div>
                            <div class="contact-item">
                                <strong>地址：</strong> 北京市朝阳区科技园区
                            </div>
                        </div>
                    </div>
                    <div class="contact-form">
                        <h3>快速咨询</h3>
                        <form>
                            <div class="form-group">
                                <input type="text" placeholder="您的姓名" required>
                            </div>
                            <div class="form-group">
                                <input type="email" placeholder="您的邮箱" required>
                            </div>
                            <div class="form-group">
                                <textarea placeholder="您的问题或需求" rows="4" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">发送消息</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 智能助手演示. 保留所有权利.</p>
        </div>
    </footer>

    <!-- 聊天助手脚本 -->
    <script>
        // 配置聊天助手 - 修正路径
        window.CHAT_WIDGET_URL = './chat-widget/';
        window.CHAT_WIDGET_AUTO_SHOW = true;  // 自动显示
        window.CHAT_WIDGET_AUTO_INIT = true;  // 自动初始化
        window.CHAT_WIDGET_POSITION = 'bottom-right';
        
        // 调试信息
        console.log('聊天助手配置已加载，URL:', window.CHAT_WIDGET_URL);
        
        // 监听聊天助手事件
        document.addEventListener('chatWidgetReady', function(event) {
            console.log('✅ 聊天助手已准备就绪:', event.detail);
        });
        
        document.addEventListener('chatWidgetError', function(event) {
            console.error('❌ 聊天助手错误:', event.detail);
        });
    </script>
    <script src="./chat-widget/embed.js"></script>
    
    <script>
        // 演示功能
        function showChatWidget() {
            console.log('🔄 尝试显示聊天助手');
            if (window.ChatWidget) {
                ChatWidget.show();
                console.log('✅ 聊天助手显示命令已发送');
            } else {
                console.error('❌ ChatWidget 未定义，正在尝试初始化...');
                alert('聊天助手未加载，请刷新页面重试');
            }
        }

        function hideChatWidget() {
            console.log('🔄 尝试隐藏聊天助手');
            if (window.ChatWidget) {
                ChatWidget.hide();
                console.log('✅ 聊天助手隐藏命令已发送');
            } else {
                console.error('❌ ChatWidget 未定义');
            }
        }

        function toggleChatWidget() {
            console.log('🔄 尝试切换聊天助手');
            if (window.ChatWidget) {
                ChatWidget.toggle();
            } else {
                showChatWidget();
            }
        }

        function sendDemoMessage() {
            console.log('🔄 尝试发送演示消息');
            if (window.ChatWidget) {
                ChatWidget.sendMessage('你好，这是一条演示消息！');
                ChatWidget.show();
                console.log('✅ 演示消息已发送');
            } else {
                console.error('❌ ChatWidget 未定义');
                showChatWidget();
            }
        }
        
        // 页面加载完成后检查聊天助手状态
        window.addEventListener('load', function() {
            console.log('📄 页面加载完成，检查聊天助手状态...');
            
            // 延迟检查，确保聊天助手有时间初始化
            setTimeout(function() {
                console.log('🔍 开始检查聊天助手状态...');
                
                if (window.ChatWidget) {
                    console.log('✅ ChatWidget 已定义');
                    if (window.ChatWidget.instance) {
                        console.log('✅ ChatWidget 实例已创建');
                    } else {
                        console.log('⚠️ ChatWidget 实例未创建，尝试初始化...');
                        window.ChatWidget.init();
                    }
                } else {
                    console.error('❌ ChatWidget 未定义，聊天助手可能加载失败');
                }
                
                // 检查DOM元素
                const container = document.getElementById('chat-widget-container');
                const iframe = document.getElementById('chat-widget-iframe');
                console.log('📦 聊天容器:', container ? '✅ 已创建' : '❌ 未创建');
                console.log('🖼️ 聊天iframe:', iframe ? '✅ 已创建' : '❌ 未创建');
                
                if (iframe) {
                    console.log('🔗 iframe src:', iframe.src);
                }
            }, 2000);
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
