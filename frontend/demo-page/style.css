/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏 */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand h1 {
    color: #667eea;
    font-size: 24px;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #667eea;
}

/* 主要内容 */
.main-content {
    margin-top: 70px;
}

/* 英雄部分 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    min-height: 600px;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 20px;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #fff;
    color: #667eea;
}

.btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* 聊天预览 */
.chat-preview {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.chat-bubble {
    background: #f1f3f4;
    padding: 15px 20px;
    border-radius: 20px;
    margin-bottom: 15px;
    color: #333;
    border-bottom-left-radius: 5px;
}

.chat-bubble.user {
    background: #667eea;
    color: white;
    margin-left: 40px;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 5px;
}

.chat-bubble p {
    margin: 0;
    font-size: 14px;
}

/* 功能部分 */
.features-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 60px;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* 演示部分 */
.demo-section {
    padding: 100px 0;
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.demo-description h3 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.demo-description p {
    color: #666;
    margin-bottom: 20px;
}

.demo-description ul {
    color: #666;
    margin-bottom: 30px;
    padding-left: 20px;
}

.demo-description li {
    margin-bottom: 8px;
}

.demo-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.demo-code {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.demo-code h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.demo-code pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    font-size: 14px;
    line-height: 1.5;
}

/* 联系部分 */
.contact-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info h3,
.contact-form h3 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.contact-info p {
    color: #666;
    margin-bottom: 30px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-item {
    color: #666;
}

.contact-item strong {
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    font-family: inherit;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

/* 页脚 */
.footer {
    background: #333;
    color: white;
    padding: 30px 0;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .demo-content,
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        display: none;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .demo-controls {
        justify-content: center;
    }
}
