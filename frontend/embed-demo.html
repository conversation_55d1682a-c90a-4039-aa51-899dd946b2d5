<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天助手嵌入演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .demo-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .demo-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .demo-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .demo-btn:hover {
            background: #0056b3;
        }
        
        .demo-btn.secondary {
            background: #6c757d;
        }
        
        .demo-btn.secondary:hover {
            background: #545b62;
        }
        
        .inline-container {
            height: 500px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            position: relative;
            background: #fafafa;
        }
        
        .inline-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #999;
        }
        
        .config-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .config-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }
        
        .config-label {
            min-width: 120px;
            font-weight: 500;
        }
        
        .config-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .config-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">智能聊天助手 - 嵌入演示</h1>
            <p class="demo-description">
                展示如何将聊天助手嵌入到不同的网站和应用中，支持多种模式和配置选项。
            </p>
        </div>

        <!-- 浮窗模式演示 -->
        <div class="demo-section">
            <h2>🎈 浮窗模式</h2>
            <p class="demo-description">
                最常用的嵌入方式，在页面右下角显示一个浮动按钮，点击后弹出聊天窗口。
                支持拖拽、收起为图标等功能。
            </p>
            
            <div class="demo-code">
&lt;script src="chat-assistant.js"&gt;&lt;/script&gt;
&lt;script&gt;
const chatAssistant = new ChatAssistant({
    mode: 'float',
    apiBaseUrl: 'http://localhost:8080/api'
});
&lt;/script&gt;
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="createFloatDemo()">启动浮窗助手</button>
                <button class="demo-btn secondary" onclick="destroyFloatDemo()">关闭浮窗助手</button>
            </div>
        </div>

        <!-- 内嵌模式演示 -->
        <div class="demo-section">
            <h2>📱 内嵌模式</h2>
            <p class="demo-description">
                将聊天助手直接嵌入到页面的指定容器中，适合作为页面的一部分使用。
            </p>
            
            <div class="demo-code">
&lt;div id="chat-container"&gt;&lt;/div&gt;
&lt;script&gt;
const chatAssistant = new ChatAssistant({
    mode: 'inline',
    container: '#chat-container',
    apiBaseUrl: 'http://localhost:8080/api'
});
&lt;/script&gt;
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="createInlineDemo()">启动内嵌助手</button>
                <button class="demo-btn secondary" onclick="destroyInlineDemo()">清空容器</button>
            </div>
            
            <div class="inline-container" id="inline-demo-container">
                <div class="inline-placeholder">
                    点击"启动内嵌助手"按钮在此处显示聊天界面
                </div>
            </div>
        </div>

        <!-- 全屏模式演示 -->
        <div class="demo-section">
            <h2>🖥️ 全屏模式</h2>
            <p class="demo-description">
                全屏显示聊天界面，适合专门的客服页面或聊天应用。
            </p>
            
            <div class="demo-code">
&lt;script&gt;
const chatAssistant = new ChatAssistant({
    mode: 'fullscreen',
    apiBaseUrl: 'http://localhost:8080/api'
});
&lt;/script&gt;
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="createFullscreenDemo()">启动全屏助手</button>
            </div>
        </div>

        <!-- 配置选项演示 -->
        <div class="demo-section">
            <h2>⚙️ 配置选项</h2>
            <p class="demo-description">
                支持丰富的配置选项，可以自定义主题、功能、文本等。
            </p>
            
            <div class="config-panel">
                <div class="config-row">
                    <label class="config-label">API地址:</label>
                    <input type="text" class="config-input" id="config-api" value="http://localhost:8080/api">
                </div>
                <div class="config-row">
                    <label class="config-label">主题色:</label>
                    <input type="color" class="config-input" id="config-color" value="#007bff">
                </div>
                <div class="config-row">
                    <label class="config-label">标题:</label>
                    <input type="text" class="config-input" id="config-title" value="智能助手">
                </div>
                <div class="config-row">
                    <label class="config-label">位置:</label>
                    <select class="config-select" id="config-position">
                        <option value="bottom-right">右下角</option>
                        <option value="bottom-left">左下角</option>
                        <option value="top-right">右上角</option>
                        <option value="top-left">左上角</option>
                    </select>
                </div>
                <div class="config-row">
                    <label class="config-label">功能:</label>
                    <label><input type="checkbox" id="config-markdown" checked> Markdown渲染</label>
                    <label><input type="checkbox" id="config-reasoning" checked> 推理过程</label>
                    <label><input type="checkbox" id="config-drag" checked> 拖拽功能</label>
                </div>
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="createCustomDemo()">使用自定义配置启动</button>
            </div>
        </div>

        <!-- API方法演示 -->
        <div class="demo-section">
            <h2>🔧 API方法</h2>
            <p class="demo-description">
                提供丰富的API方法，可以通过JavaScript控制聊天助手的行为。
            </p>
            
            <div class="demo-code">
// 创建实例
const chat = new ChatAssistant(config);

// 控制显示/隐藏
chat.open();
chat.close();

// 发送消息
chat.sendUserMessage('你好');

// 会话管理
chat.newChat();        // 新建会话
chat.clearMessages();  // 清空消息

// 更新配置
chat.updateConfig({ theme: { primaryColor: '#ff6b6b' } });
            </div>
            
            <div class="demo-buttons">
                <button class="demo-btn" onclick="testAPI('open')">打开助手</button>
                <button class="demo-btn" onclick="testAPI('close')">关闭助手</button>
                <button class="demo-btn" onclick="testAPI('message')">发送测试消息</button>
                <button class="demo-btn" onclick="testAPI('newchat')">新建会话</button>
                <button class="demo-btn" onclick="testAPI('clear')">清空消息</button>
            </div>
        </div>
    </div>

    <!-- 引入聊天助手 -->
    <script src="chat-assistant.js"></script>
    
    <script>
        let floatDemo = null;
        let inlineDemo = null;
        let fullscreenDemo = null;
        let currentDemo = null;

        function createFloatDemo() {
            destroyFloatDemo();
            floatDemo = new ChatAssistant({
                mode: 'float',
                apiBaseUrl: 'http://localhost:8080/api',
                callbacks: {
                    onOpen: () => console.log('浮窗助手已打开'),
                    onClose: () => console.log('浮窗助手已关闭'),
                    onMessage: (msg) => console.log('新消息:', msg)
                }
            });
            currentDemo = floatDemo;
        }

        function destroyFloatDemo() {
            if (floatDemo) {
                floatDemo.close();
                // 移除DOM元素
                const container = document.querySelector('.chat-assistant-container.chat-assistant-float');
                const trigger = document.querySelector('.chat-assistant-trigger');
                if (container) container.remove();
                if (trigger) trigger.remove();
                floatDemo = null;
            }
        }

        function createInlineDemo() {
            destroyInlineDemo();
            inlineDemo = new ChatAssistant({
                mode: 'inline',
                container: '#inline-demo-container',
                apiBaseUrl: 'http://localhost:8080/api'
            });
            currentDemo = inlineDemo;
        }

        function destroyInlineDemo() {
            const container = document.getElementById('inline-demo-container');
            container.innerHTML = '<div class="inline-placeholder">点击"启动内嵌助手"按钮在此处显示聊天界面</div>';
            inlineDemo = null;
        }

        function createFullscreenDemo() {
            fullscreenDemo = new ChatAssistant({
                mode: 'fullscreen',
                apiBaseUrl: 'http://localhost:8080/api',
                callbacks: {
                    onClose: () => {
                        fullscreenDemo = null;
                    }
                }
            });
            currentDemo = fullscreenDemo;
        }

        function createCustomDemo() {
            destroyFloatDemo();
            
            const config = {
                mode: 'float',
                apiBaseUrl: document.getElementById('config-api').value,
                theme: {
                    primaryColor: document.getElementById('config-color').value
                },
                texts: {
                    title: document.getElementById('config-title').value
                },
                float: {
                    position: document.getElementById('config-position').value
                },
                features: {
                    markdown: document.getElementById('config-markdown').checked,
                    reasoning: document.getElementById('config-reasoning').checked,
                    drag: document.getElementById('config-drag').checked
                }
            };
            
            floatDemo = new ChatAssistant(config);
            currentDemo = floatDemo;
        }

        function testAPI(action) {
            if (!currentDemo) {
                alert('请先启动一个聊天助手实例');
                return;
            }
            
            switch (action) {
                case 'open':
                    currentDemo.open();
                    break;
                case 'close':
                    currentDemo.close();
                    break;
                case 'message':
                    currentDemo.sendUserMessage('这是一条测试消息，请回复一些包含Markdown格式的内容。');
                    break;
                case 'newchat':
                    currentDemo.newChat();
                    break;
                case 'clear':
                    currentDemo.clearMessages();
                    break;
            }
        }
    </script>
</body>
</html>
