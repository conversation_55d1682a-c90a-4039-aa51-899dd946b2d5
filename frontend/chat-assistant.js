/**
 * 智能聊天助手 - 统一嵌入版本
 * 支持多种嵌入模式：浮窗、内嵌、全屏
 * 一行代码即可集成到任何网站
 */

(function(global) {
    'use strict';

    // 默认配置
    const DEFAULT_CONFIG = {
        // API配置
        apiBaseUrl: 'http://localhost:8080/api',
        
        // 显示模式: 'float' | 'inline' | 'fullscreen'
        mode: 'float',
        
        // 容器选择器（inline模式使用）
        container: null,
        
        // 浮窗配置
        float: {
            position: 'bottom-right', // 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
            offset: { x: 20, y: 20 },
            size: { width: 380, height: 600 }
        },
        
        // 主题配置
        theme: {
            primaryColor: '#007bff',
            backgroundColor: '#ffffff',
            textColor: '#333333',
            borderRadius: '12px'
        },
        
        // 功能配置
        features: {
            history: true,
            reasoning: true,
            markdown: true,
            drag: true,
            minimize: true
        },
        
        // 文本配置
        texts: {
            title: '智能助手',
            placeholder: '输入您的问题...',
            welcome: '您好！我是您的智能助手，有什么可以帮助您的吗？',
            sendButton: '发送',
            historyButton: '历史记录',
            minimizeButton: '最小化',
            closeButton: '关闭'
        },
        
        // 回调函数
        callbacks: {
            onOpen: null,
            onClose: null,
            onMessage: null,
            onError: null
        }
    };

    // CSS样式模板
    const CSS_TEMPLATE = `
        .chat-assistant-container {
            --primary-color: {{primaryColor}};
            --bg-color: {{backgroundColor}};
            --text-color: {{textColor}};
            --border-radius: {{borderRadius}};
            
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-color);
            box-sizing: border-box;
            z-index: 999999;
        }
        
        .chat-assistant-container *,
        .chat-assistant-container *::before,
        .chat-assistant-container *::after {
            box-sizing: inherit;
        }
        
        /* 浮窗模式样式 */
        .chat-assistant-float {
            position: fixed;
            width: {{floatWidth}}px;
            height: {{floatHeight}}px;
            background: var(--bg-color);
            border-radius: var(--border-radius);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .chat-assistant-float.minimized {
            height: 60px;
        }
        
        /* 内嵌模式样式 */
        .chat-assistant-inline {
            width: 100%;
            height: 100%;
            background: var(--bg-color);
            border-radius: var(--border-radius);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* 全屏模式样式 */
        .chat-assistant-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-color);
            display: flex;
            flex-direction: column;
            z-index: 999999;
        }
        
        /* 浮动按钮 */
        .chat-assistant-trigger {
            position: fixed;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transition: transform 0.2s;
            z-index: 999998;
        }
        
        .chat-assistant-trigger:hover {
            transform: scale(1.05);
        }
        
        .chat-assistant-trigger-icon {
            font-size: 24px;
            color: white;
        }
        
        /* 头部样式 */
        .chat-assistant-header {
            background: var(--primary-color);
            color: white;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: move;
        }
        
        .chat-assistant-title {
            font-weight: 600;
            font-size: 16px;
            margin: 0;
        }
        
        .chat-assistant-controls {
            display: flex;
            gap: 8px;
        }
        
        .chat-assistant-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .chat-assistant-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* 消息区域 */
        .chat-assistant-messages {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background: #f8f9fa;
        }
        
        .chat-assistant-message {
            margin-bottom: 16px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-assistant-message.user {
            align-items: flex-end;
        }
        
        .chat-assistant-message.bot {
            align-items: flex-start;
        }
        
        .chat-assistant-message-content {
            max-width: 95%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .chat-assistant-message.user .chat-assistant-message-content {
            background: var(--primary-color);
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .chat-assistant-message.bot .chat-assistant-message-content {
            background: white;
            color: var(--text-color);
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 4px;
            position: relative;
        }

        .chat-assistant-message.bot .chat-assistant-message-content:hover .chat-assistant-copy-btn {
            opacity: 1;
        }
        
        .chat-assistant-message-time {
            font-size: 11px;
            color: #666;
            margin-top: 4px;
            padding: 0 4px;
        }
        
        /* 输入区域 */
        .chat-assistant-input-area {
            border-top: 1px solid #e1e5e9;
            padding: 12px 16px;
            background: var(--bg-color);
        }
        
        .chat-assistant-input-container {
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }
        
        .chat-assistant-input {
            flex: 1;
            border: 1px solid #e1e5e9;
            border-radius: 20px;
            padding: 10px 16px;
            font-size: 14px;
            resize: none;
            outline: none;
            max-height: 100px;
            min-height: 20px;
            font-family: inherit;
        }
        
        .chat-assistant-input:focus {
            border-color: var(--primary-color);
        }
        
        .chat-assistant-send-btn {
            background: var(--primary-color);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            flex-shrink: 0;
        }
        
        .chat-assistant-send-btn:hover {
            opacity: 0.8;
        }
        
        .chat-assistant-send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 推理过程样式 */
        .chat-assistant-reasoning-section {
            max-width: 95%;
            margin-bottom: 8px;
            border: 1px solid #E5E5EA;
            border-radius: 12px;
            background: #FAFAFA;
            overflow: hidden;
        }

        .chat-assistant-reasoning-header {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #F0F0F5;
            cursor: pointer;
            user-select: none;
            border-bottom: 1px solid #E5E5EA;
        }

        .chat-assistant-reasoning-header:hover {
            background: #E8E8ED;
        }

        .chat-assistant-reasoning-icon {
            margin-right: 6px;
            font-size: 14px;
        }

        .chat-assistant-reasoning-title {
            flex: 1;
            font-size: 13px;
            font-weight: 500;
            color: #3C3C43;
        }

        .chat-assistant-reasoning-toggle {
            font-size: 12px;
            color: #8E8E93;
            transition: transform 0.2s ease;
        }

        .chat-assistant-reasoning-content {
            padding: 10px 12px;
            font-size: 12px;
            line-height: 1.4;
            color: #666;
            white-space: pre-wrap;
            background: #FAFAFA;
            border-top: 1px solid #F0F0F0;
        }

        .chat-assistant-main-content {
            margin-top: 0;
        }

        /* 加载动画 */
        .chat-assistant-typing-indicator {
            display: flex;
            align-items: center;
            padding: 10px 14px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            max-width: 80%;
        }

        .chat-assistant-typing-dots {
            display: flex;
            gap: 4px;
        }

        .chat-assistant-typing-dot {
            width: 6px;
            height: 6px;
            background: #666;
            border-radius: 50%;
            animation: chat-assistant-typing 1.4s infinite ease-in-out;
        }

        .chat-assistant-typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .chat-assistant-typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes chat-assistant-typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* Markdown样式 */
        .chat-assistant-message-text h1,
        .chat-assistant-message-text h2,
        .chat-assistant-message-text h3,
        .chat-assistant-message-text h4,
        .chat-assistant-message-text h5,
        .chat-assistant-message-text h6 {
            margin: 16px 0 8px 0;
            font-weight: 600;
            line-height: 1.25;
        }

        .chat-assistant-message-text h1 { font-size: 1.5em; }
        .chat-assistant-message-text h2 { font-size: 1.3em; }
        .chat-assistant-message-text h3 { font-size: 1.1em; }

        .chat-assistant-message-text p {
            margin: 8px 0;
            white-space: pre-wrap;
        }

        .chat-assistant-message-text ul,
        .chat-assistant-message-text ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .chat-assistant-message-text li {
            margin: 4px 0;
        }

        .chat-assistant-message-text blockquote {
            margin: 8px 0;
            padding: 8px 12px;
            border-left: 4px solid #ddd;
            background: rgba(0, 0, 0, 0.05);
            font-style: italic;
        }

        .chat-assistant-message-text code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }

        .chat-assistant-message-text pre {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .chat-assistant-message-text pre code {
            background: none;
            padding: 0;
            border-radius: 0;
            font-size: inherit;
        }

        .chat-assistant-message-text table {
            border-collapse: collapse;
            margin: 8px 0;
            width: 100%;
        }

        .chat-assistant-message-text th,
        .chat-assistant-message-text td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            text-align: left;
        }

        .chat-assistant-message-text th {
            background: #f6f8fa;
            font-weight: 600;
        }

        .chat-assistant-message-text a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .chat-assistant-message-text a:hover {
            text-decoration: underline;
        }

        /* 滚动条样式 */
        .chat-assistant-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-assistant-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chat-assistant-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-assistant-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 复制按钮样式 */
        .chat-assistant-copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 4px;
            width: 28px;
            height: 28px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.2s ease;
            font-size: 12px;
            color: #666;
        }

        .chat-assistant-copy-btn:hover {
            background: rgba(0, 0, 0, 0.2);
            color: #333;
        }

        .chat-assistant-copy-btn.copied {
            background: #4caf50;
            color: white;
        }

        .chat-assistant-copy-btn.copied:hover {
            background: #45a049;
        }

        /* 复制成功提示 */
        .chat-assistant-copy-tooltip {
            position: absolute;
            top: -30px;
            right: 0;
            background: #333;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            transform: translateY(5px);
            transition: all 0.2s ease;
            pointer-events: none;
            z-index: 1000;
        }

        .chat-assistant-copy-tooltip.show {
            opacity: 1;
            transform: translateY(0);
        }

        .chat-assistant-copy-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            right: 8px;
            border: 4px solid transparent;
            border-top-color: #333;
        }

        /* 历史记录面板样式 */
        .chat-assistant-history-panel {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: {{backgroundColor}};
            z-index: 100;
            display: flex;
            flex-direction: column;
        }

        .chat-assistant-history-header {
            padding: 16px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .chat-assistant-history-header h4 {
            margin: 0;
            font-size: 16px;
            color: {{textColor}};
            font-weight: 600;
        }

        .chat-assistant-history-controls {
            display: flex;
            gap: 8px;
        }

        .chat-assistant-history-list {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        .chat-assistant-history-item {
            padding: 12px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }

        .chat-assistant-history-item:hover {
            background: #f8f9fa;
            border-color: {{primaryColor}};
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .chat-assistant-history-item-title {
            font-weight: 600;
            font-size: 14px;
            color: {{textColor}};
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .chat-assistant-history-item-preview {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .chat-assistant-history-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #999;
        }

        .chat-assistant-history-item-time {
            font-size: 11px;
            color: #999;
        }

        .chat-assistant-history-item-count {
            background: {{primaryColor}};
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        .chat-assistant-history-empty {
            text-align: center;
            color: #999;
            padding: 40px 20px;
            font-size: 14px;
        }

        .chat-assistant-history-empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    `;

    // HTML模板
    const HTML_TEMPLATE = `
        <div class="chat-assistant-header">
            <h3 class="chat-assistant-title">{{title}}</h3>
            <div class="chat-assistant-controls">
                {{#if features.history}}
                <button class="chat-assistant-btn" data-action="history" title="{{texts.historyButton}}">📋</button>
                {{/if}}
                {{#if features.minimize}}
                <button class="chat-assistant-btn" data-action="minimize" title="{{texts.minimizeButton}}">−</button>
                {{/if}}
                <button class="chat-assistant-btn" data-action="close" title="{{texts.closeButton}}">×</button>
            </div>
        </div>
        <div class="chat-assistant-messages">
            <div class="chat-assistant-message bot">
                <div class="chat-assistant-message-content">{{texts.welcome}}</div>
            </div>
        </div>

        <!-- 历史记录面板 -->
        <div class="chat-assistant-history-panel" id="chat-assistant-history-panel" style="display: none;">
            <div class="chat-assistant-history-header">
                <h4>聊天历史</h4>
                <div class="chat-assistant-history-controls">
                    <button class="chat-assistant-btn" data-action="clear-history" title="清空历史">🗑️</button>
                    <button class="chat-assistant-btn" data-action="close-history" title="关闭">×</button>
                </div>
            </div>
            <div class="chat-assistant-history-list" id="chat-assistant-history-list">
                <!-- 历史记录项将在这里动态生成 -->
            </div>
        </div>

        <div class="chat-assistant-input-area">
            <div class="chat-assistant-input-container">
                <textarea class="chat-assistant-input" placeholder="{{texts.placeholder}}" rows="1"></textarea>
                <button class="chat-assistant-send-btn" title="{{texts.sendButton}}">➤</button>
            </div>
        </div>
    `;

    // 主类定义
    class ChatAssistant {
        constructor(config = {}) {
            this.config = this.mergeConfig(DEFAULT_CONFIG, config);
            this.isOpen = false;
            this.isMinimized = false;
            this.isDragging = false;
            this.messages = [];
            this.currentChatId = this.generateChatId();
            this.chatHistories = [];
            this.isHistoryVisible = false;

            this.init();
        }

        mergeConfig(defaultConfig, userConfig) {
            const merged = JSON.parse(JSON.stringify(defaultConfig));
            
            function deepMerge(target, source) {
                for (const key in source) {
                    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                        target[key] = target[key] || {};
                        deepMerge(target[key], source[key]);
                    } else {
                        target[key] = source[key];
                    }
                }
            }
            
            deepMerge(merged, userConfig);
            return merged;
        }

        generateChatId() {
            return 'chat_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
        }

        init() {
            this.injectStyles();
            this.createElements();
            this.bindEvents();
            this.loadExternalLibraries();
            this.chatHistories = this.loadChatHistories();

            if (this.config.mode === 'inline' && this.config.container) {
                this.show();
            }
        }

        injectStyles() {
            if (document.getElementById('chat-assistant-styles')) return;

            const style = document.createElement('style');
            style.id = 'chat-assistant-styles';

            let css = CSS_TEMPLATE
                .replace(/{{primaryColor}}/g, this.config.theme.primaryColor)
                .replace(/{{backgroundColor}}/g, this.config.theme.backgroundColor)
                .replace(/{{textColor}}/g, this.config.theme.textColor)
                .replace(/{{borderRadius}}/g, this.config.theme.borderRadius)
                .replace(/{{floatWidth}}/g, this.config.float.size.width)
                .replace(/{{floatHeight}}/g, this.config.float.size.height);

            style.textContent = css;
            document.head.appendChild(style);
        }

        loadExternalLibraries() {
            if (!this.config.features.markdown) return;

            // 加载Markdown解析库
            if (typeof marked === 'undefined') {
                const script1 = document.createElement('script');
                script1.src = 'https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js';
                document.head.appendChild(script1);
            }

            // 加载代码高亮库
            if (typeof Prism === 'undefined') {
                const script2 = document.createElement('script');
                script2.src = 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js';
                document.head.appendChild(script2);

                const script3 = document.createElement('script');
                script3.src = 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js';
                document.head.appendChild(script3);

                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css';
                document.head.appendChild(link);
            }
        }

        createElements() {
            // 创建主容器
            this.container = document.createElement('div');
            this.container.className = 'chat-assistant-container';

            // 根据模式设置样式
            if (this.config.mode === 'float') {
                this.container.classList.add('chat-assistant-float');
                this.setFloatPosition();

                // 创建触发按钮
                this.createTriggerButton();
            } else if (this.config.mode === 'inline') {
                this.container.classList.add('chat-assistant-inline');
            } else if (this.config.mode === 'fullscreen') {
                this.container.classList.add('chat-assistant-fullscreen');
            }

            // 设置HTML内容
            this.container.innerHTML = this.renderTemplate(HTML_TEMPLATE, {
                title: this.config.texts.title,
                texts: this.config.texts,
                features: this.config.features
            });

            // 获取关键元素引用
            this.messagesContainer = this.container.querySelector('.chat-assistant-messages');
            this.inputElement = this.container.querySelector('.chat-assistant-input');
            this.sendButton = this.container.querySelector('.chat-assistant-send-btn');
            this.header = this.container.querySelector('.chat-assistant-header');
            this.historyPanel = this.container.querySelector('.chat-assistant-history-panel');
            this.historyList = this.container.querySelector('.chat-assistant-history-list');

            // 添加到页面
            if (this.config.mode === 'inline' && this.config.container) {
                const targetContainer = typeof this.config.container === 'string'
                    ? document.querySelector(this.config.container)
                    : this.config.container;
                if (targetContainer) {
                    targetContainer.appendChild(this.container);
                }
            } else {
                document.body.appendChild(this.container);
            }

            // 初始状态
            if (this.config.mode === 'float') {
                this.container.style.display = 'none';
            }
        }

        createTriggerButton() {
            this.triggerButton = document.createElement('div');
            this.triggerButton.className = 'chat-assistant-trigger';
            this.triggerButton.innerHTML = '<span class="chat-assistant-trigger-icon">💬</span>';

            this.setFloatPosition(this.triggerButton);
            document.body.appendChild(this.triggerButton);

            this.triggerButton.addEventListener('click', () => {
                this.toggle();
            });
        }

        setFloatPosition(element = this.container) {
            if (!element) return;

            const { position, offset } = this.config.float;
            const style = element.style;

            switch (position) {
                case 'bottom-right':
                    style.bottom = offset.y + 'px';
                    style.right = offset.x + 'px';
                    break;
                case 'bottom-left':
                    style.bottom = offset.y + 'px';
                    style.left = offset.x + 'px';
                    break;
                case 'top-right':
                    style.top = offset.y + 'px';
                    style.right = offset.x + 'px';
                    break;
                case 'top-left':
                    style.top = offset.y + 'px';
                    style.left = offset.x + 'px';
                    break;
            }
        }

        renderTemplate(template, data) {
            return template.replace(/{{(.*?)}}/g, (match, key) => {
                const keys = key.trim().split('.');
                let value = data;
                for (const k of keys) {
                    value = value && value[k];
                }
                return value || '';
            }).replace(/{{#if (.*?)}}(.*?){{\/if}}/gs, (match, condition, content) => {
                const keys = condition.trim().split('.');
                let value = data;
                for (const k of keys) {
                    value = value && value[k];
                }
                return value ? content : '';
            });
        }

        bindEvents() {
            // 发送按钮事件
            this.sendButton.addEventListener('click', () => {
                this.sendMessage();
            });

            // 输入框事件
            this.inputElement.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // 控制按钮事件
            this.container.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action) {
                    this.handleAction(action);
                }
            });

            // 拖拽功能
            if (this.config.features.drag && this.config.mode === 'float') {
                this.bindDragEvents();
            }
        }

        handleAction(action) {
            switch (action) {
                case 'close':
                    this.hide();
                    break;
                case 'minimize':
                    this.toggleMinimize();
                    break;
                case 'history':
                    this.showHistory();
                    break;
                case 'close-history':
                    this.hideHistory();
                    break;
                case 'clear-history':
                    this.clearHistory();
                    break;
            }
        }

        bindDragEvents() {
            let isDragging = false;
            let startX, startY, startLeft, startTop;

            this.header.addEventListener('mousedown', (e) => {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                startLeft = parseInt(this.container.style.left || 0);
                startTop = parseInt(this.container.style.top || 0);

                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            });

            const onMouseMove = (e) => {
                if (!isDragging) return;

                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                this.container.style.left = (startLeft + deltaX) + 'px';
                this.container.style.top = (startTop + deltaY) + 'px';
                this.container.style.right = 'auto';
                this.container.style.bottom = 'auto';
            };

            const onMouseUp = () => {
                isDragging = false;
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            };
        }

        show() {
            this.container.style.display = 'flex';
            this.isOpen = true;

            if (this.triggerButton) {
                this.triggerButton.style.display = 'none';
            }

            this.callCallback('onOpen');
        }

        hide() {
            this.container.style.display = 'none';
            this.isOpen = false;

            if (this.triggerButton) {
                this.triggerButton.style.display = 'flex';
            }

            this.callCallback('onClose');
        }

        toggle() {
            if (this.isOpen) {
                this.hide();
            } else {
                this.show();
            }
        }

        toggleMinimize() {
            this.isMinimized = !this.isMinimized;
            this.container.classList.toggle('minimized', this.isMinimized);
        }

        callCallback(name, ...args) {
            const callback = this.config.callbacks[name];
            if (typeof callback === 'function') {
                callback.apply(this, args);
            }
        }

        async sendMessage() {
            const message = this.inputElement.value.trim();
            if (!message) return;

            // 清空输入框
            this.inputElement.value = '';

            // 添加用户消息
            this.addMessage('user', message);

            // 显示加载状态
            this.showTypingIndicator();

            try {
                await this.streamChatResponse(message);
            } catch (error) {
                console.error('发送消息失败:', error);
                this.addMessage('bot', '抱歉，发生了错误，请稍后再试。');
                this.callCallback('onError', error);
            } finally {
                this.hideTypingIndicator();
                // 自动保存聊天历史
                this.saveCurrentChat();
            }
        }

        addMessage(role, content, timestamp = null, saveToArray = true) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-assistant-message ${role}`;

            const messageContent = document.createElement('div');
            messageContent.className = 'chat-assistant-message-content';

            const messageText = document.createElement('div');
            messageText.className = 'chat-assistant-message-text';

            // 根据角色和配置决定是否渲染Markdown
            if (role === 'bot' && this.config.features.markdown && typeof marked !== 'undefined') {
                messageText.innerHTML = marked.parse(content || '');
            } else {
                messageText.textContent = content;
            }

            messageContent.appendChild(messageText);

            // 为机器人消息添加复制按钮
            if (role === 'bot') {
                const copyBtn = this.createCopyButton(content);
                messageContent.appendChild(copyBtn);
            }

            const messageTime = document.createElement('div');
            messageTime.className = 'chat-assistant-message-time';
            messageTime.textContent = timestamp || this.formatTime(new Date());

            messageDiv.appendChild(messageContent);
            messageDiv.appendChild(messageTime);

            this.messagesContainer.appendChild(messageDiv);
            this.scrollToBottom();

            // 保存消息到内存
            if (saveToArray) {
                this.messages.push({
                    role,
                    content,
                    timestamp: timestamp || new Date().toISOString()
                });
            }

            this.callCallback('onMessage', { role, content, timestamp });
            return messageDiv;
        }

        addMessageWithReasoning(role, content, reasoning, timestamp = null, saveToArray = true) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-assistant-message ${role}`;

            // 推理过程区域（可折叠）
            if (role === 'bot' && this.config.features.reasoning) {
                const reasoningSection = document.createElement('div');
                reasoningSection.className = 'chat-assistant-reasoning-section';

                const reasoningHeader = document.createElement('div');
                reasoningHeader.className = 'chat-assistant-reasoning-header';
                reasoningHeader.innerHTML = `
                    <span class="chat-assistant-reasoning-icon">🧠</span>
                    <span class="chat-assistant-reasoning-title">思考过程</span>
                    <span class="chat-assistant-reasoning-toggle">▲</span>
                `;

                const reasoningContentDiv = document.createElement('div');
                reasoningContentDiv.className = 'chat-assistant-reasoning-content';
                reasoningContentDiv.textContent = reasoning;

                // 为推理过程添加复制按钮
                const reasoningCopyBtn = this.createCopyButton(reasoning);
                reasoningCopyBtn.style.position = 'absolute';
                reasoningCopyBtn.style.top = '8px';
                reasoningCopyBtn.style.right = '8px';
                reasoningSection.style.position = 'relative';
                reasoningSection.appendChild(reasoningCopyBtn);

                reasoningSection.appendChild(reasoningHeader);
                reasoningSection.appendChild(reasoningContentDiv);

                // 添加折叠/展开功能
                reasoningHeader.addEventListener('click', () => {
                    const isCollapsed = reasoningContentDiv.style.display === 'none';
                    reasoningContentDiv.style.display = isCollapsed ? 'block' : 'none';
                    reasoningHeader.querySelector('.chat-assistant-reasoning-toggle').textContent = isCollapsed ? '▲' : '▼';
                });

                messageDiv.appendChild(reasoningSection);
            }

            // 主要回复内容
            const mainContent = document.createElement('div');
            mainContent.className = 'chat-assistant-message-content chat-assistant-main-content';

            const messageText = document.createElement('div');
            messageText.className = 'chat-assistant-message-text';

            // 渲染Markdown内容
            if (this.config.features.markdown && typeof marked !== 'undefined') {
                messageText.innerHTML = marked.parse(content || '');
            } else {
                messageText.textContent = content;
            }

            mainContent.appendChild(messageText);

            // 为机器人消息添加复制按钮
            if (role === 'bot') {
                const copyBtn = this.createCopyButton(content);
                mainContent.appendChild(copyBtn);

                // 如果有推理过程，添加"复制全部"按钮
                if (reasoning) {
                    const copyAllBtn = this.createCopyAllButton(reasoning, content);
                    copyAllBtn.style.right = '40px'; // 避免与普通复制按钮重叠
                    mainContent.appendChild(copyAllBtn);
                }
            }

            const messageTime = document.createElement('div');
            messageTime.className = 'chat-assistant-message-time';
            messageTime.textContent = timestamp || this.formatTime(new Date());

            messageDiv.appendChild(mainContent);
            messageDiv.appendChild(messageTime);

            this.messagesContainer.appendChild(messageDiv);
            this.scrollToBottom();

            // 保存消息到内存
            if (saveToArray) {
                this.messages.push({
                    role,
                    content,
                    reasoning: reasoning,
                    timestamp: timestamp || new Date().toISOString()
                });
            }

            this.callCallback('onMessage', { role, content, reasoning, timestamp });
            return messageDiv;
        }

        showTypingIndicator() {
            this.hideTypingIndicator(); // 确保只有一个

            const typingDiv = document.createElement('div');
            typingDiv.className = 'chat-assistant-message bot';
            typingDiv.id = 'chat-assistant-typing-indicator';

            const typingContent = document.createElement('div');
            typingContent.className = 'chat-assistant-typing-indicator';

            const typingDots = document.createElement('div');
            typingDots.className = 'chat-assistant-typing-dots';

            for (let i = 0; i < 3; i++) {
                const dot = document.createElement('div');
                dot.className = 'chat-assistant-typing-dot';
                typingDots.appendChild(dot);
            }

            typingContent.appendChild(typingDots);
            typingDiv.appendChild(typingContent);

            this.messagesContainer.appendChild(typingDiv);
            this.scrollToBottom();
        }

        hideTypingIndicator() {
            const typingIndicator = document.getElementById('chat-assistant-typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        async streamChatResponse(userMessage) {
            const requestBody = {
                chatId: this.currentChatId,
                stream: true,
                detail: false,
                responseChatItemId: 'response_' + Date.now(),
                variables: {
                    uid: 'user_' + Date.now(),
                    name: '用户'
                },
                messages: [
                    ...this.messages.slice(-10), // 只发送最近10条消息作为上下文
                    { role: 'user', content: userMessage }
                ]
            };

            const response = await fetch(`${this.config.apiBaseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let botMessageDiv = null;
            let botMessageContent = '';
            let reasoningContent = '';
            let reasoningDiv = null;
            let hasReasoning = false;

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            // 处理可能的双重data:前缀
                            let data = line.slice(5).trim(); // 移除 'data:'
                            if (data.startsWith('data:')) {
                                data = data.slice(5).trim(); // 再次移除 'data:'
                            }

                            if (data === '[DONE]') {
                                return;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                const delta = parsed.choices?.[0]?.delta;

                                // 处理推理过程内容
                                if (delta?.reasoning_content && this.config.features.reasoning) {
                                    hasReasoning = true;
                                    if (!botMessageDiv) {
                                        this.hideTypingIndicator();
                                        botMessageDiv = this.addMessageWithReasoning('bot', '', '', null, false);
                                        botMessageContent = '';
                                        reasoningContent = '';
                                        reasoningDiv = botMessageDiv.querySelector('.chat-assistant-reasoning-content');
                                    }

                                    reasoningContent += delta.reasoning_content;
                                    if (reasoningDiv) {
                                        reasoningDiv.textContent = reasoningContent;

                                        // 更新推理过程的复制按钮
                                        const reasoningCopyBtn = botMessageDiv.querySelector('.chat-assistant-reasoning-section .chat-assistant-copy-btn');
                                        if (reasoningCopyBtn) {
                                            this.updateCopyButtonContent(reasoningCopyBtn, reasoningContent);
                                        }
                                    }
                                    this.scrollToBottom();
                                }

                                // 处理实际的回复内容（用户可见）
                                if (delta?.content) {
                                    if (!botMessageDiv) {
                                        this.hideTypingIndicator();
                                        // 根据是否有推理内容决定使用哪种消息类型
                                        if (hasReasoning && this.config.features.reasoning) {
                                            botMessageDiv = this.addMessageWithReasoning('bot', '', '', null, false);
                                            reasoningDiv = botMessageDiv.querySelector('.chat-assistant-reasoning-content');
                                        } else {
                                            botMessageDiv = this.addMessage('bot', '', null, false);
                                        }
                                        botMessageContent = '';
                                        reasoningContent = '';
                                    }

                                    botMessageContent += delta.content;
                                    // 根据消息类型选择正确的文本元素
                                    const messageText = hasReasoning && this.config.features.reasoning ?
                                        botMessageDiv.querySelector('.chat-assistant-main-content .chat-assistant-message-text') :
                                        botMessageDiv.querySelector('.chat-assistant-message-text');
                                    if (messageText) {
                                        // 实时渲染Markdown
                                        if (this.config.features.markdown && typeof marked !== 'undefined') {
                                            messageText.innerHTML = marked.parse(botMessageContent || '');
                                        } else {
                                            messageText.textContent = botMessageContent;
                                        }
                                    }

                                    // 更新复制按钮的内容
                                    const copyBtns = hasReasoning && this.config.features.reasoning ?
                                        botMessageDiv.querySelectorAll('.chat-assistant-main-content .chat-assistant-copy-btn') :
                                        botMessageDiv.querySelectorAll('.chat-assistant-copy-btn');

                                    copyBtns.forEach((copyBtn, index) => {
                                        if (copyBtn.innerHTML === '📋') {
                                            // 普通复制按钮
                                            this.updateCopyButtonContent(copyBtn, botMessageContent);
                                        } else if (copyBtn.innerHTML === '📄' || copyBtn.getAttribute('data-copy-type') === 'all') {
                                            // 复制全部按钮
                                            const fullContent = `思考过程：\n${reasoningContent}\n\n回复内容：\n${botMessageContent}`;
                                            this.updateCopyButtonContent(copyBtn, fullContent);
                                        }
                                    });

                                    this.scrollToBottom();
                                }
                            } catch (e) {
                                console.log('解析流式数据失败:', e, '数据:', data);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();

                // 流式响应结束后，保存完整的消息到数组中
                if (botMessageDiv && (botMessageContent || reasoningContent)) {
                    // 移除之前可能添加的不完整消息
                    const lastMessage = this.messages[this.messages.length - 1];
                    if (lastMessage && lastMessage.role === 'bot' && (!lastMessage.content || lastMessage.content === '')) {
                        this.messages.pop();
                    }

                    // 添加完整的消息
                    this.messages.push({
                        role: 'bot',
                        content: botMessageContent,
                        reasoning: reasoningContent || undefined,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }

        formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        scrollToBottom() {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }

        createCopyButton(content) {
            const copyBtn = document.createElement('button');
            copyBtn.className = 'chat-assistant-copy-btn';
            copyBtn.innerHTML = '📋';
            copyBtn.title = '复制内容';
            copyBtn.setAttribute('data-copy-content', content);

            // 创建提示框
            const tooltip = document.createElement('div');
            tooltip.className = 'chat-assistant-copy-tooltip';
            tooltip.textContent = '已复制！';
            copyBtn.appendChild(tooltip);

            copyBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const contentToCopy = copyBtn.getAttribute('data-copy-content') || content;
                this.copyToClipboard(contentToCopy, copyBtn, tooltip);
            });

            copyBtn.setAttribute('data-event-bound', 'true');
            return copyBtn;
        }

        createCopyAllButton(reasoning, content) {
            const copyAllBtn = document.createElement('button');
            copyAllBtn.className = 'chat-assistant-copy-btn';
            copyAllBtn.innerHTML = '📄';
            copyAllBtn.title = '复制全部（包含思考过程）';

            const fullContent = `思考过程：\n${reasoning}\n\n回复内容：\n${content}`;
            copyAllBtn.setAttribute('data-copy-content', fullContent);
            copyAllBtn.setAttribute('data-copy-type', 'all');

            // 创建提示框
            const tooltip = document.createElement('div');
            tooltip.className = 'chat-assistant-copy-tooltip';
            tooltip.textContent = '已复制全部！';
            copyAllBtn.appendChild(tooltip);

            copyAllBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const contentToCopy = copyAllBtn.getAttribute('data-copy-content') || fullContent;
                this.copyToClipboard(contentToCopy, copyAllBtn, tooltip);
            });

            copyAllBtn.setAttribute('data-event-bound', 'true');
            return copyAllBtn;
        }

        async copyToClipboard(text, button, tooltip) {
            try {
                // 优先使用现代的 Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(text);
                } else {
                    // 降级到传统方法
                    this.fallbackCopyToClipboard(text);
                }

                // 显示复制成功状态
                this.showCopySuccess(button, tooltip);

            } catch (err) {
                console.error('复制失败:', err);
                // 尝试降级方法
                try {
                    this.fallbackCopyToClipboard(text);
                    this.showCopySuccess(button, tooltip);
                } catch (fallbackErr) {
                    console.error('降级复制也失败:', fallbackErr);
                    this.showCopyError(button, tooltip);
                }
            }
        }

        fallbackCopyToClipboard(text) {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (!successful) {
                    throw new Error('execCommand copy failed');
                }
            } finally {
                document.body.removeChild(textArea);
            }
        }

        showCopySuccess(button, tooltip) {
            // 更新按钮状态
            button.innerHTML = '✓';
            button.classList.add('copied');
            button.title = '复制成功！';

            // 显示提示框
            tooltip.textContent = '已复制！';
            tooltip.classList.add('show');

            // 2秒后恢复原状
            setTimeout(() => {
                button.innerHTML = '📋';
                button.classList.remove('copied');
                button.title = '复制内容';
                tooltip.classList.remove('show');
            }, 2000);
        }

        showCopyError(button, tooltip) {
            // 更新按钮状态
            button.innerHTML = '✗';
            button.style.background = '#f44336';
            button.title = '复制失败';

            // 显示错误提示
            tooltip.textContent = '复制失败';
            tooltip.style.background = '#f44336';
            tooltip.classList.add('show');

            // 2秒后恢复原状
            setTimeout(() => {
                button.innerHTML = '📋';
                button.style.background = '';
                button.title = '复制内容';
                tooltip.textContent = '已复制！';
                tooltip.style.background = '#333';
                tooltip.classList.remove('show');
            }, 2000);
        }

        updateCopyButtonContent(copyBtn, newContent) {
            // 直接更新按钮的数据属性，避免重新创建按钮
            copyBtn.setAttribute('data-copy-content', newContent);

            // 如果按钮还没有绑定事件监听器，则绑定
            if (!copyBtn.hasAttribute('data-event-bound')) {
                const tooltip = copyBtn.querySelector('.chat-assistant-copy-tooltip');
                const self = this; // 保存this引用

                copyBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const contentToCopy = copyBtn.getAttribute('data-copy-content') || newContent;
                    self.copyToClipboard(contentToCopy, copyBtn, tooltip);
                });

                copyBtn.setAttribute('data-event-bound', 'true');
            }
        }

        showHistory() {
            this.isHistoryVisible = true;
            this.historyPanel.style.display = 'flex';
            this.renderHistoryList();
        }

        hideHistory() {
            this.isHistoryVisible = false;
            this.historyPanel.style.display = 'none';
        }

        clearHistory() {
            if (confirm('确定要清空所有聊天历史吗？此操作不可撤销。')) {
                this.chatHistories = [];
                this.saveChatHistories();
                this.renderHistoryList();
            }
        }

        loadChatHistories() {
            try {
                const saved = localStorage.getItem('chat-assistant-histories');
                return saved ? JSON.parse(saved) : [];
            } catch (error) {
                console.error('加载聊天历史失败:', error);
                return [];
            }
        }

        saveChatHistories() {
            try {
                localStorage.setItem('chat-assistant-histories', JSON.stringify(this.chatHistories));
            } catch (error) {
                console.error('保存聊天历史失败:', error);
            }
        }

        saveCurrentChat() {
            if (this.messages.length === 0) return;

            // 查找是否已存在当前聊天记录
            const existingIndex = this.chatHistories.findIndex(h => h.chatId === this.currentChatId);

            // 生成聊天标题（使用第一条用户消息）
            const firstUserMessage = this.messages.find(m => m.role === 'user');
            const title = firstUserMessage ?
                (firstUserMessage.content.length > 30 ?
                    firstUserMessage.content.substring(0, 30) + '...' :
                    firstUserMessage.content) :
                '新对话';

            const chatHistory = {
                chatId: this.currentChatId,
                title: title,
                messages: [...this.messages],
                createdAt: this.messages[0]?.timestamp || new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                messageCount: this.messages.length
            };

            if (existingIndex >= 0) {
                // 更新现有记录
                this.chatHistories[existingIndex] = chatHistory;
            } else {
                // 添加新记录
                this.chatHistories.unshift(chatHistory);
            }

            // 限制历史记录数量（最多保存50个）
            if (this.chatHistories.length > 50) {
                this.chatHistories = this.chatHistories.slice(0, 50);
            }

            this.saveChatHistories();
        }

        renderHistoryList() {
            if (!this.historyList) return;

            this.historyList.innerHTML = '';

            if (this.chatHistories.length === 0) {
                const emptyDiv = document.createElement('div');
                emptyDiv.className = 'chat-assistant-history-empty';
                emptyDiv.innerHTML = `
                    <div class="chat-assistant-history-empty-icon">📝</div>
                    <div>暂无聊天历史</div>
                    <div style="font-size: 12px; margin-top: 8px; color: #ccc;">开始对话后会自动保存历史记录</div>
                `;
                this.historyList.appendChild(emptyDiv);
                return;
            }

            this.chatHistories.forEach((history, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'chat-assistant-history-item';

                // 生成预览文本（第一条机器人回复）
                const firstBotMessage = history.messages.find(m => m.role === 'bot');
                const preview = firstBotMessage ?
                    (firstBotMessage.content.length > 60 ?
                        firstBotMessage.content.substring(0, 60) + '...' :
                        firstBotMessage.content) :
                    '暂无回复';

                historyItem.innerHTML = `
                    <div class="chat-assistant-history-item-title">${history.title}</div>
                    <div class="chat-assistant-history-item-preview">${preview}</div>
                    <div class="chat-assistant-history-item-meta">
                        <span class="chat-assistant-history-item-time">${this.formatTime(new Date(history.updatedAt))}</span>
                        <span class="chat-assistant-history-item-count">${history.messageCount} 条消息</span>
                    </div>
                `;

                historyItem.addEventListener('click', () => {
                    this.loadChatHistory(history);
                });

                this.historyList.appendChild(historyItem);
            });
        }

        loadChatHistory(history) {
            // 保存当前聊天
            this.saveCurrentChat();

            // 清空当前消息
            this.messages = [];
            this.messagesContainer.innerHTML = '';

            // 加载历史消息
            this.currentChatId = history.chatId;
            this.messages = [...history.messages];

            // 渲染历史消息
            this.messages.forEach(message => {
                if (message.reasoning) {
                    this.addMessageWithReasoning(message.role, message.content, message.reasoning, this.formatTime(new Date(message.timestamp)));
                } else {
                    this.addMessage(message.role, message.content, this.formatTime(new Date(message.timestamp)));
                }
            });

            // 关闭历史面板
            this.hideHistory();
        }

        startNewChat() {
            // 保存当前聊天
            this.saveCurrentChat();

            // 开始新聊天
            this.currentChatId = this.generateChatId();
            this.messages = [];
            this.messagesContainer.innerHTML = '';

            // 显示欢迎消息
            const welcomeDiv = document.createElement('div');
            welcomeDiv.className = 'chat-assistant-message bot';
            welcomeDiv.innerHTML = `
                <div class="chat-assistant-message-content">${this.config.texts.welcome}</div>
            `;
            this.messagesContainer.appendChild(welcomeDiv);
        }

        // 公共API方法
        open() {
            this.show();
        }

        close() {
            this.hide();
        }

        minimize() {
            this.toggleMinimize();
        }

        sendUserMessage(message) {
            this.inputElement.value = message;
            this.sendMessage();
        }

        clearMessages() {
            this.messagesContainer.innerHTML = '';
            this.messages = [];
        }

        updateConfig(newConfig) {
            this.config = this.mergeConfig(this.config, newConfig);
            // 重新应用样式等
            this.injectStyles();
        }
    }

    // 导出到全局
    global.ChatAssistant = ChatAssistant;

    // 便捷初始化方法
    global.initChatAssistant = function(config) {
        return new ChatAssistant(config);
    };

})(window);
