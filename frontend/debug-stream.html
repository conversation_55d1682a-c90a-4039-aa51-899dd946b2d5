<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应调试</title>
    <link rel="stylesheet" href="chat-widget/style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .debug-header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .debug-content {
            padding: 20px;
            display: flex;
            gap: 20px;
        }
        .debug-controls {
            flex: 1;
        }
        .debug-chat {
            flex: 1;
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            display: block;
            width: 100%;
        }
        .debug-button:hover {
            background: #0056b3;
        }
        .messages-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 16px;
            background: #f8f9fa;
        }
        .log-area {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-header">
            <h1>流式响应调试工具</h1>
        </div>
        <div class="debug-content">
            <div class="debug-controls">
                <h3>测试控制</h3>
                <button class="debug-button" onclick="simulateReasoningStream()">模拟带推理的流式响应</button>
                <button class="debug-button" onclick="simulateNormalStream()">模拟普通流式响应</button>
                <button class="debug-button" onclick="clearAll()">清空所有</button>
                
                <div class="log-area" id="logArea">
                    <div>调试日志将在这里显示...</div>
                </div>
            </div>
            
            <div class="debug-chat">
                <h3>聊天显示</h3>
                <div class="messages-container" id="messagesContainer">
                    <!-- 消息将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script src="chat-widget/chat-widget.js"></script>
    <script>
        class DebugChatWidget extends ChatWidget {
            constructor() {
                super();
                this.messagesContainer = document.getElementById('messagesContainer');
                this.logArea = document.getElementById('logArea');
            }

            log(message) {
                const logDiv = document.createElement('div');
                logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                this.logArea.appendChild(logDiv);
                this.logArea.scrollTop = this.logArea.scrollHeight;
            }

            clear() {
                this.messagesContainer.innerHTML = '';
                this.logArea.innerHTML = '<div>调试日志将在这里显示...</div>';
                this.messages = [];
            }

            // 模拟流式响应处理
            async simulateStreamResponse(chunks) {
                let botMessageDiv = null;
                let botMessageContent = '';
                let reasoningContent = '';
                let reasoningDiv = null;
                let hasReasoning = false;

                this.log('开始模拟流式响应');

                for (const chunk of chunks) {
                    this.log(`处理chunk: ${JSON.stringify(chunk)}`);
                    
                    const delta = chunk.choices?.[0]?.delta;
                    if (!delta) continue;

                    // 处理推理过程内容
                    if (delta.reasoning_content) {
                        this.log(`检测到推理内容: ${delta.reasoning_content}`);
                        hasReasoning = true;
                        if (!botMessageDiv) {
                            this.log('创建带推理的消息');
                            botMessageDiv = this.addMessageWithReasoning('bot', '', '');
                            botMessageContent = '';
                            reasoningContent = '';
                            reasoningDiv = botMessageDiv.querySelector('.reasoning-content');
                            this.log(`推理内容元素: ${reasoningDiv ? '找到' : '未找到'}`);
                        }

                        reasoningContent += delta.reasoning_content;
                        if (reasoningDiv) {
                            reasoningDiv.textContent = reasoningContent;
                        }
                        this.scrollToBottom();
                    }

                    // 处理实际的回复内容
                    if (delta.content) {
                        this.log(`检测到回复内容: ${delta.content}`);
                        if (!botMessageDiv) {
                            // 根据是否有推理内容决定使用哪种消息类型
                            if (hasReasoning) {
                                this.log('创建带推理的消息（内容阶段）');
                                botMessageDiv = this.addMessageWithReasoning('bot', '', '');
                                reasoningDiv = botMessageDiv.querySelector('.reasoning-content');
                            } else {
                                this.log('创建普通消息');
                                botMessageDiv = this.addMessage('bot', '');
                            }
                            botMessageContent = '';
                            reasoningContent = '';
                        }

                        botMessageContent += delta.content;
                        // 根据消息类型选择正确的文本元素
                        const messageText = hasReasoning ? 
                            botMessageDiv.querySelector('.main-content p') : 
                            botMessageDiv.querySelector('p');
                        this.log(`消息文本元素: ${messageText ? '找到' : '未找到'}, 是否有推理: ${hasReasoning}`);
                        if (messageText) {
                            messageText.textContent = botMessageContent;
                        }
                        this.scrollToBottom();
                    }

                    // 模拟延迟
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                this.log('流式响应模拟完成');
            }
        }

        const debugWidget = new DebugChatWidget();

        async function simulateReasoningStream() {
            const chunks = [
                {
                    choices: [{
                        delta: {
                            reasoning_content: "用户询问了一个关于"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            reasoning_content: "机器学习的问题。"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            reasoning_content: "我需要提供一个清晰、准确的解释。"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            content: "机器学习是"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            content: "人工智能的一个分支，"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            content: "它使计算机能够学习和改进。"
                        }
                    }]
                }
            ];

            await debugWidget.simulateStreamResponse(chunks);
        }

        async function simulateNormalStream() {
            const chunks = [
                {
                    choices: [{
                        delta: {
                            content: "这是一条"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            content: "普通的回复消息，"
                        }
                    }]
                },
                {
                    choices: [{
                        delta: {
                            content: "没有推理过程。"
                        }
                    }]
                }
            ];

            await debugWidget.simulateStreamResponse(chunks);
        }

        function clearAll() {
            debugWidget.clear();
        }
    </script>
</body>
</html>
