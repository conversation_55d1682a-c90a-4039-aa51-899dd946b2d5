<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推理过程测试</title>
    <link rel="stylesheet" href="chat-widget/style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .test-header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .test-content {
            padding: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .messages-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 16px;
            background: #f8f9fa;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>推理过程显示测试</h1>
        </div>
        <div class="test-content">
            <p>点击下面的按钮测试不同类型的消息显示：</p>
            
            <button class="test-button" onclick="testReasoningMessage()">测试带推理过程的消息</button>
            <button class="test-button" onclick="testNormalMessage()">测试普通消息</button>
            <button class="test-button" onclick="clearMessages()">清空消息</button>
            
            <div class="messages-container" id="messagesContainer">
                <!-- 消息将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        class TestChatWidget {
            constructor() {
                this.messagesContainer = document.getElementById('messagesContainer');
                this.messages = [];
            }

            addMessage(role, content, timestamp = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}-message`;

                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';

                const messageText = document.createElement('p');
                messageText.textContent = content;
                messageContent.appendChild(messageText);

                const messageTime = document.createElement('div');
                messageTime.className = 'message-time';
                messageTime.textContent = timestamp || this.formatTime(new Date());

                messageDiv.appendChild(messageContent);
                messageDiv.appendChild(messageTime);

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();

                return messageDiv;
            }

            addMessageWithReasoning(role, content, reasoning, timestamp = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${role}-message`;

                // 推理过程区域（可折叠）
                if (role === 'bot') {
                    const reasoningSection = document.createElement('div');
                    reasoningSection.className = 'reasoning-section';
                    
                    const reasoningHeader = document.createElement('div');
                    reasoningHeader.className = 'reasoning-header';
                    reasoningHeader.innerHTML = `
                        <span class="reasoning-icon">🧠</span>
                        <span class="reasoning-title">思考过程</span>
                        <span class="reasoning-toggle">▲</span>
                    `;
                    
                    const reasoningContentDiv = document.createElement('div');
                    reasoningContentDiv.className = 'reasoning-content';
                    reasoningContentDiv.textContent = reasoning || '正在思考...';
                    
                    reasoningSection.appendChild(reasoningHeader);
                    reasoningSection.appendChild(reasoningContentDiv);
                    
                    // 添加折叠/展开功能
                    reasoningHeader.addEventListener('click', () => {
                        const isCollapsed = reasoningContentDiv.style.display === 'none';
                        reasoningContentDiv.style.display = isCollapsed ? 'block' : 'none';
                        reasoningHeader.querySelector('.reasoning-toggle').textContent = isCollapsed ? '▲' : '▼';
                    });
                    
                    messageDiv.appendChild(reasoningSection);
                }

                // 主要回复内容
                const mainContent = document.createElement('div');
                mainContent.className = 'main-content message-content';

                const messageText = document.createElement('p');
                messageText.textContent = content;
                mainContent.appendChild(messageText);

                const messageTime = document.createElement('div');
                messageTime.className = 'message-time';
                messageTime.textContent = timestamp || this.formatTime(new Date());

                messageDiv.appendChild(mainContent);
                messageDiv.appendChild(messageTime);

                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();

                return messageDiv;
            }

            formatTime(date) {
                return date.toLocaleTimeString('zh-CN', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }

            clear() {
                this.messagesContainer.innerHTML = '';
                this.messages = [];
            }
        }

        const testWidget = new TestChatWidget();

        function testReasoningMessage() {
            const reasoning = "用户询问了一个关于机器学习的问题。我需要提供一个清晰、准确的解释。首先，我应该定义什么是机器学习，然后解释它的基本原理和应用场景。";
            const content = "机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。通过分析大量数据，机器学习算法可以识别模式并做出预测或决策。";
            
            testWidget.addMessageWithReasoning('bot', content, reasoning);
        }

        function testNormalMessage() {
            const content = "这是一条普通的回复消息，没有推理过程显示。";
            testWidget.addMessage('bot', content);
        }

        function clearMessages() {
            testWidget.clear();
        }
    </script>
</body>
</html>
