# 智能聊天助手配置示例
# 复制此文件为 backend/src/main/resources/application.yml 并修改相应配置

server:
  port: 8080

spring:
  application:
    name: chat-assistant

# 聊天API配置
chat:
  api:
    # AI模型服务的基础URL
    base-url: http://localhost:3000
    
    # API访问令牌
    token: fastgpt-xxxxxx
    
    # 应用ID
    app-id: your-app-id

# 日志配置
logging:
  level:
    com.example.chatassistant: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/chat-assistant.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
