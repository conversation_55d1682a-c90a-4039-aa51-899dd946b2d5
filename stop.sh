#!/bin/bash

# 智能聊天助手停止脚本

echo "🛑 停止智能聊天助手项目..."

# 停止后端服务
if [ -f ".backend.pid" ]; then
    BACKEND_PID=$(cat .backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        echo "🔄 停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        
        # 等待进程结束
        sleep 3
        
        # 如果进程仍在运行，强制终止
        if ps -p $BACKEND_PID > /dev/null; then
            echo "⚠️  强制终止后端服务..."
            kill -9 $BACKEND_PID
        fi
        
        echo "✅ 后端服务已停止"
    else
        echo "⚠️  后端服务进程不存在"
    fi
    rm .backend.pid
else
    echo "⚠️  未找到后端服务PID文件"
fi

# 停止前端服务
if [ -f ".frontend.pid" ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        echo "🔄 停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        
        # 等待进程结束
        sleep 2
        
        # 如果进程仍在运行，强制终止
        if ps -p $FRONTEND_PID > /dev/null; then
            echo "⚠️  强制终止前端服务..."
            kill -9 $FRONTEND_PID
        fi
        
        echo "✅ 前端服务已停止"
    else
        echo "⚠️  前端服务进程不存在"
    fi
    rm .frontend.pid
else
    echo "⚠️  未找到前端服务PID文件"
fi

# 清理日志文件（可选）
read -p "🗑️  是否删除日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "backend.log" ]; then
        rm backend.log
        echo "✅ 已删除 backend.log"
    fi
    if [ -f "frontend.log" ]; then
        rm frontend.log
        echo "✅ 已删除 frontend.log"
    fi
fi

echo ""
echo "🎉 项目已完全停止！"
