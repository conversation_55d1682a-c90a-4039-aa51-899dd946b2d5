# 编译输出
backend/target/
backend/build/
*.class

# 日志文件
*.log
logs/
backend.log
frontend.log

# 进程ID文件
.backend.pid
.frontend.pid

# IDE文件
.idea/
.vscode/
*.iml
*.ipr
*.iws

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
*.swp
*.swo

# 配置文件（包含敏感信息）
backend/src/main/resources/application-prod.yml
backend/src/main/resources/application-local.yml

# Node.js（如果使用）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 打包文件
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Maven
.mvn/
mvnw
mvnw.cmd

# Gradle
.gradle/
gradle/
gradlew
gradlew.bat

# 测试覆盖率
coverage/
*.lcov

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
