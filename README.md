# 智能聊天助手项目

一个基于iframe的智能聊天助手解决方案，支持流式对话、历史记录管理和可拖拽界面。

## 项目架构

```
assistant_demo/
├── backend/                    # Spring Boot后端服务
│   ├── src/main/java/
│   │   └── com/example/chatassistant/
│   │       ├── controller/     # API控制器
│   │       ├── service/        # 业务服务
│   │       ├── dto/           # 数据传输对象
│   │       └── config/        # 配置类
│   ├── src/main/resources/
│   └── pom.xml
├── frontend/                   # 前端资源
│   ├── chat-widget/           # 聊天助手组件
│   │   ├── style.css
│   │   ├── chat-widget.js
│   │   └── embed.js           # 嵌入脚本
│   ├── index.html
│   └── style.css
└── README.md
```

## 功能特性

### 前端功能
- ✅ 可拖拽的聊天窗口
- ✅ 流式消息显示
- ✅ 历史对话记录
- ✅ 最小化/关闭功能
- ✅ 响应式设计
- ✅ iframe嵌入支持

### 后端功能
- ✅ Spring Boot RESTful API
- ✅ 流式响应支持
- ✅ CORS跨域配置
- ✅ 请求参数补充
- ✅ 代理转发到AI模型

### 集成功能
- ✅ 简单的JavaScript集成
- ✅ 可配置的初始化选项
- ✅ 事件监听支持
- ✅ 多种控制API

## 快速开始

### 1. 启动后端服务

```bash
cd backend
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 2. 配置AI模型API

编辑 `backend/src/main/resources/application.yml`：

```yaml
chat:
  api:
    base-url: http://your-ai-model-server:3000  # 替换为您的AI模型服务地址
    token: your-api-token                        # 替换为您的API令牌
    app-id: your-app-id                         # 替换为您的应用ID
```

### 3. 启动前端演示

使用任何HTTP服务器启动前端，例如：

```bash
# 使用Python
cd frontend/demo-page
python -m http.server 3000

# 或使用Node.js
npx serve frontend -p 3000
```

访问 `http://localhost:3000` 查看演示页面

## API接口

### 聊天对话接口

**POST** `/api/chat/completions`

支持流式响应，兼容OpenAI v1接口规范。

请求示例：
```json
{
    "chatId": "my_chatId",
    "stream": true,
    "detail": false,
    "responseChatItemId": "my_responseChatItemId",
    "variables": {
        "uid": "user123",
        "name": "张三"
    },
    "messages": [
        {
            "role": "user",
            "content": "你好"
        }
    ]
}
```

### 历史记录接口

**POST** `/api/chat/histories`

获取用户的历史对话列表。

**POST** `/api/chat/records`

获取特定对话的消息记录。

## 集成指南

### 基础集成

在您的网页中添加以下代码：

```html
<!-- 引入聊天助手脚本 -->
<script src="http://localhost:8080/chat-widget/embed.js"></script>

<script>
// 初始化聊天助手
ChatWidget.init({
    widgetUrl: 'http://localhost:8080/chat-widget/',
    autoShow: false,
    position: 'bottom-right'
});
</script>
```

### 配置选项

```javascript
ChatWidget.init({
    widgetUrl: 'http://localhost:8080/chat-widget/',  // 聊天组件URL
    autoShow: false,                                   // 是否自动显示
    position: 'bottom-right',                         // 初始位置
    theme: 'default'                                  // 主题
});
```

### API方法

```javascript
// 显示聊天助手
ChatWidget.show();

// 隐藏聊天助手
ChatWidget.hide();

// 切换显示状态
ChatWidget.toggle();

// 发送消息
ChatWidget.sendMessage('你好！');

// 更新配置
ChatWidget.setConfig({ theme: 'dark' });

// 销毁实例
ChatWidget.destroy();
```

### 事件监听

```javascript
// 聊天助手准备就绪
document.addEventListener('chatWidgetReady', function(event) {
    console.log('聊天助手已准备就绪');
});

// 聊天助手最小化
document.addEventListener('chatWidgetMinimize', function() {
    console.log('聊天助手已最小化');
});

// 聊天助手关闭
document.addEventListener('chatWidgetClose', function() {
    console.log('聊天助手已关闭');
});
```

## 开发说明

### 技术栈

**后端：**
- Spring Boot 3.2.0
- Spring WebFlux (流式响应)
- Maven

**前端：**
- 原生JavaScript (ES6+)
- CSS3
- HTML5

### 开发环境要求

- Java 17+
- Maven 3.6+
- 现代浏览器支持

### 自定义开发

1. **修改样式**：编辑 `frontend/chat-widget/style.css`
2. **扩展功能**：修改 `frontend/chat-widget/chat-widget.js`
3. **后端逻辑**：修改 `backend/src/main/java/com/example/chatassistant/`

## 部署指南

### 后端部署

```bash
cd backend
mvn clean package
java -jar target/chat-assistant-1.0.0.jar
```

### 前端部署

将 `frontend/` 目录下的文件部署到任何静态文件服务器或CDN。

### Docker部署

```dockerfile
# Dockerfile示例
FROM openjdk:17-jdk-slim
COPY backend/target/chat-assistant-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 故障排除

### 常见问题

1. **CORS错误**：确保后端CORS配置正确
2. **连接失败**：检查API地址和端口配置
3. **样式异常**：确认CSS文件路径正确
4. **iframe不显示**：检查浏览器安全策略

### 调试模式

在浏览器控制台中启用调试：

```javascript
localStorage.setItem('chatWidgetDebug', 'true');
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请联系：<EMAIL>
