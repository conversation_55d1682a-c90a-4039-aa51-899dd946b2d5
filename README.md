# 🤖 智能聊天助手

一个现代化的智能聊天助手系统，支持**一行代码嵌入**、**多种显示模式**、**Markdown渲染**、**推理过程显示**等强大功能。

## ✨ 核心特性

### 🚀 极简嵌入
```html
<!-- 只需要这一行代码！ -->
<script src="chat-assistant.js"></script>
<script>new ChatAssistant();</script>
```

### 🎨 多种模式
- **浮窗模式**: 页面右下角浮动按钮，点击弹出聊天窗口
- **内嵌模式**: 直接嵌入到页面指定容器中
- **全屏模式**: 全屏显示聊天界面

### 🧠 智能功能
- **推理过程显示**: 展示AI的思考过程，增强用户信任
- **Markdown渲染**: 自动美化代码块、表格、列表等格式
- **流式响应**: 实时显示AI回复内容
- **代码高亮**: 支持多种编程语言语法高亮

### 🔧 灵活配置
- **自定义主题**: 支持颜色、字体、圆角等样式配置
- **功能开关**: 可选择启用/禁用特定功能
- **多语言支持**: 可自定义所有界面文本
- **回调函数**: 支持消息、打开、关闭等事件回调

## 🏗️ 项目架构

```
assistant_demo/
├── backend/                    # Spring Boot后端服务
│   ├── src/main/java/
│   │   └── com/example/chatassistant/
│   │       ├── controller/     # API控制器
│   │       ├── service/        # 业务服务
│   │       ├── dto/           # 数据传输对象
│   │       └── config/        # 配置类
│   ├── src/main/resources/
│   └── pom.xml
├── frontend/                   # 前端资源
│   ├── chat-assistant.js      # 🆕 统一嵌入文件（核心）
│   ├── embed-demo.html        # 🆕 详细演示页面
│   ├── simple-embed.html      # 🆕 简单演示页面
│   ├── chat-widget/           # 原有组件（保留兼容）
│   ├── index.html
│   └── style.css
├── start.sh                   # 一键启动脚本
├── stop.sh                    # 一键停止脚本
└── README.md
```

## 🎯 系统架构

```
前端 (chat-assistant.js) ←→ 中间层 (Spring Boot) ←→ AI服务 (OpenAI兼容API)
```

- **前端**: 单文件JavaScript库，零依赖，支持多种嵌入方式
- **中间层**: Spring Boot提供API代理和数据处理
- **AI服务**: 兼容OpenAI API格式的AI模型服务

## 功能特性

### 前端功能
- ✅ 可拖拽的聊天窗口
- ✅ 流式消息显示
- ✅ 历史对话记录
- ✅ 最小化/关闭功能
- ✅ 响应式设计
- ✅ iframe嵌入支持

### 后端功能
- ✅ Spring Boot RESTful API
- ✅ 流式响应支持
- ✅ CORS跨域配置
- ✅ 请求参数补充
- ✅ 代理转发到AI模型

### 集成功能
- ✅ 简单的JavaScript集成
- ✅ 可配置的初始化选项
- ✅ 事件监听支持
- ✅ 多种控制API

## 🚀 快速开始

### 方式一：一键启动（推荐）

```bash
# 克隆项目
git clone https://git.haipi8.top:1001/zhijg/assistant_demo.git
cd assistant_demo

# 一键启动（自动构建后端 + 启动前端演示）
./start.sh

# 访问演示页面
open http://localhost:3001
```

### 方式二：手动启动

#### 1. 启动后端服务

```bash
cd backend
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

#### 2. 配置AI模型API

编辑 `backend/src/main/resources/application.yml`：

```yaml
chat:
  api:
    base-url: http://your-ai-model-server:3000  # 替换为您的AI模型服务地址
    token: your-api-token                        # 替换为您的API令牌
    app-id: your-app-id                         # 替换为您的应用ID
```

#### 3. 启动前端演示

```bash
# 使用Python
cd frontend
python -m http.server 3001

# 或使用Node.js
npx serve frontend -p 3001
```

访问演示页面：
- 简单演示：`http://localhost:3001/simple-embed.html`
- 详细演示：`http://localhost:3001/embed-demo.html`

## API接口

### 聊天对话接口

**POST** `/api/chat/completions`

支持流式响应，兼容OpenAI v1接口规范。

请求示例：
```json
{
    "chatId": "my_chatId",
    "stream": true,
    "detail": false,
    "responseChatItemId": "my_responseChatItemId",
    "variables": {
        "uid": "user123",
        "name": "张三"
    },
    "messages": [
        {
            "role": "user",
            "content": "你好"
        }
    ]
}
```

### 历史记录接口

**POST** `/api/chat/histories`

获取用户的历史对话列表。

**POST** `/api/chat/records`

获取特定对话的消息记录。

## 📖 集成指南

### 🎈 浮窗模式（最常用）

```html
<!-- 引入聊天助手 -->
<script src="chat-assistant.js"></script>
<script>
// 一行代码启动浮窗助手
new ChatAssistant({
    apiBaseUrl: 'http://localhost:8080/api'
});
</script>
```

### 📱 内嵌模式

```html
<!-- 创建容器 -->
<div id="chat-container" style="width: 400px; height: 600px;"></div>

<!-- 引入并初始化 -->
<script src="chat-assistant.js"></script>
<script>
new ChatAssistant({
    mode: 'inline',
    container: '#chat-container',
    apiBaseUrl: 'http://localhost:8080/api'
});
</script>
```

### 🖥️ 全屏模式

```html
<script src="chat-assistant.js"></script>
<script>
new ChatAssistant({
    mode: 'fullscreen',
    apiBaseUrl: 'http://localhost:8080/api'
});
</script>
```

### ⚙️ 高级配置

```javascript
const chatAssistant = new ChatAssistant({
    // 基础配置
    mode: 'float',                              // 显示模式
    apiBaseUrl: 'http://localhost:8080/api',    // API地址
    container: '#chat-container',               // 容器（inline模式）

    // 浮窗配置
    float: {
        position: 'bottom-right',               // 位置
        offset: { x: 20, y: 20 },              // 偏移
        size: { width: 380, height: 600 }      // 尺寸
    },

    // 主题配置
    theme: {
        primaryColor: '#007bff',                // 主色调
        backgroundColor: '#ffffff',            // 背景色
        textColor: '#333333',                  // 文字色
        borderRadius: '12px'                   // 圆角
    },

    // 功能配置
    features: {
        history: true,                         // 历史记录
        reasoning: true,                       // 推理过程
        markdown: true,                        // Markdown渲染
        drag: true,                           // 拖拽功能
        minimize: true                        // 最小化
    },

    // 文本配置
    texts: {
        title: '智能助手',
        placeholder: '输入您的问题...',
        welcome: '您好！我是您的智能助手，有什么可以帮助您的吗？'
    },

    // 回调函数
    callbacks: {
        onOpen: () => console.log('助手已打开'),
        onClose: () => console.log('助手已关闭'),
        onMessage: (msg) => console.log('新消息:', msg),
        onError: (err) => console.error('错误:', err)
    }
});
```

### 🔧 API方法

```javascript
// 控制显示/隐藏
chatAssistant.open();                          // 打开助手
chatAssistant.close();                         // 关闭助手
chatAssistant.toggle();                        // 切换显示状态
chatAssistant.minimize();                      // 最小化

// 消息操作
chatAssistant.sendUserMessage('你好！');        // 发送消息
chatAssistant.clearMessages();                 // 清空消息

// 配置更新
chatAssistant.updateConfig({                   // 更新配置
    theme: { primaryColor: '#ff6b6b' }
});
```

## 开发说明

### 技术栈

**后端：**
- Spring Boot 3.2.0
- Spring WebFlux (流式响应)
- Maven

**前端：**
- 原生JavaScript (ES6+)
- CSS3
- HTML5

### 开发环境要求

- Java 17+
- Maven 3.6+
- 现代浏览器支持

### 自定义开发

1. **修改样式**：编辑 `frontend/chat-widget/style.css`
2. **扩展功能**：修改 `frontend/chat-widget/chat-widget.js`
3. **后端逻辑**：修改 `backend/src/main/java/com/example/chatassistant/`

## 部署指南

### 后端部署

```bash
cd backend
mvn clean package
java -jar target/chat-assistant-1.0.0.jar
```

### 前端部署

将 `frontend/` 目录下的文件部署到任何静态文件服务器或CDN。

### Docker部署

```dockerfile
# Dockerfile示例
FROM openjdk:17-jdk-slim
COPY backend/target/chat-assistant-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 故障排除

### 常见问题

1. **CORS错误**：确保后端CORS配置正确
2. **连接失败**：检查API地址和端口配置
3. **样式异常**：确认CSS文件路径正确
4. **iframe不显示**：检查浏览器安全策略

### 调试模式

在浏览器控制台中启用调试：

```javascript
localStorage.setItem('chatWidgetDebug', 'true');
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题，请联系：<EMAIL>
