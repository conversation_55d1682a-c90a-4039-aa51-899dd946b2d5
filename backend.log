
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-06-21T20:36:57.618+08:00  INFO 97771 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Starting ChatAssistantApplication v1.0.0 using Java 17.0.3 with PID 97771 (/Users/<USER>/augment-projects/assistant_demo/backend/target/chat-assistant-1.0.0.jar started by zhi<PERSON><PERSON> in /Users/<USER>/augment-projects/assistant_demo/backend)
2025-06-21T20:36:57.619+08:00 DEBUG 97771 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-21T20:36:57.619+08:00  INFO 97771 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : No active profile set, falling back to 1 default profile: "default"
2025-06-21T20:36:58.193+08:00  INFO 97771 --- [chat-assistant] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-21T20:36:58.198+08:00  INFO 97771 --- [chat-assistant] [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-21T20:36:58.199+08:00  INFO 97771 --- [chat-assistant] [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-21T20:36:58.222+08:00  INFO 97771 --- [chat-assistant] [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-21T20:36:58.223+08:00  INFO 97771 --- [chat-assistant] [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 546 ms
2025-06-21T20:36:58.464+08:00 DEBUG 97771 --- [chat-assistant] [           main] s.w.s.m.m.a.RequestMappingHandlerMapping : 6 mappings in 'requestMappingHandlerMapping'
2025-06-21T20:36:58.485+08:00 DEBUG 97771 --- [chat-assistant] [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-21T20:36:58.532+08:00 DEBUG 97771 --- [chat-assistant] [           main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-21T20:36:58.548+08:00 DEBUG 97771 --- [chat-assistant] [           main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-21T20:36:58.590+08:00  INFO 97771 --- [chat-assistant] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-21T20:36:58.599+08:00  INFO 97771 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Started ChatAssistantApplication in 1.24 seconds (process running for 1.497)
2025-06-21T20:37:07.139+08:00  INFO 97771 --- [chat-assistant] [nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21T20:37:07.140+08:00  INFO 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-21T20:37:07.140+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-06-21T20:37:07.140+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-06-21T20:37:07.140+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-06-21T20:37:07.140+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@10231de5
2025-06-21T20:37:07.141+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@3b97a22b
2025-06-21T20:37:07.141+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-21T20:37:07.141+08:00  INFO 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-06-21T20:37:07.146+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/health", parameters={}
2025-06-21T20:37:07.149+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#health()
2025-06-21T20:37:07.164+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-21T20:37:07.165+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [{service=chat-assistant, status=UP, timestamp=2025-06-21T12:37:07.155541Z}]
2025-06-21T20:37:07.175+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-06-21T20:37:25.919+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : POST "/api/chat/completions", parameters={}
2025-06-21T20:37:25.921+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-2] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#streamChat(ChatRequest)
2025-06-21T20:37:25.983+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-2] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [com.example.chatassistant.dto.ChatRequest@216a01e3]
2025-06-21T20:37:26.018+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-2] o.s.w.r.f.client.ExchangeFunctions       : [5b6f74b2] HTTP POST http://localhost:3000/api/v1/chat/completions
2025-06-21T20:37:26.059+08:00 ERROR 97771 --- [chat-assistant] [nio-8080-exec-2] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-06-21T20:37:26.093+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-2] o.s.w.c.request.async.WebAsyncManager    : Started async request
2025-06-21T20:37:26.094+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Exiting but response remains open for further handling
2025-06-21T20:37:26.139+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-2] org.springframework.web.HttpLogging      : [5b6f74b2] Encoding [com.example.chatassistant.dto.ChatRequest@216a01e3]
2025-06-21T20:37:26.163+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions       : [5b6f74b2] [78534b50-1] Response 200 OK
2025-06-21T20:37:26.172+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-2] org.springframework.web.HttpLogging      : [5b6f74b2] [78534b50-1] Decoded "event: error"
2025-06-21T20:37:26.173+08:00 DEBUG 97771 --- [chat-assistant] [         task-1] org.springframework.web.HttpLogging      : [5b6f74b2] [78534b50-1] Decoded "data: {"code":514,"statusText":"unAuthApiKey","message":"common:code_error.error_message.514","data" (truncated)..."
2025-06-21T20:37:26.174+08:00 DEBUG 97771 --- [chat-assistant] [         task-2] org.springframework.web.HttpLogging      : [5b6f74b2] [78534b50-1] Decoded ""
2025-06-21T20:37:26.176+08:00 DEBUG 97771 --- [chat-assistant] [         task-3] o.s.w.c.request.async.WebAsyncManager    : Async result set, dispatch to /api/chat/completions
2025-06-21T20:37:26.178+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-3] o.s.web.servlet.DispatcherServlet        : "ASYNC" dispatch for POST "/api/chat/completions", parameters={}
2025-06-21T20:37:26.178+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-3] s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-06-21T20:37:26.180+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-06-21T20:37:26.180+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Nothing to write: null body
2025-06-21T20:37:26.181+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-3] o.s.web.servlet.DispatcherServlet        : Exiting from "ASYNC" dispatch, status 200
2025-06-21T20:37:46.367+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-4] o.s.web.servlet.DispatcherServlet        : GET "/api/health", parameters={}
2025-06-21T20:37:46.367+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-4] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#health()
2025-06-21T20:37:46.368+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-21T20:37:46.368+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-4] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [{service=chat-assistant, status=UP, timestamp=2025-06-21T12:37:46.368197Z}]
2025-06-21T20:37:46.369+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-4] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-06-21T20:39:48.980+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-7] o.s.web.servlet.DispatcherServlet        : POST "/api/chat/completions", parameters={}
2025-06-21T20:39:48.980+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-7] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#streamChat(ChatRequest)
2025-06-21T20:39:48.981+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [com.example.chatassistant.dto.ChatRequest@18ae141]
2025-06-21T20:39:48.981+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions       : [f4d1f00] HTTP POST http://localhost:3000/api/v1/chat/completions
2025-06-21T20:39:48.983+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-7] o.s.w.c.request.async.WebAsyncManager    : Started async request
2025-06-21T20:39:48.983+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-7] o.s.web.servlet.DispatcherServlet        : Exiting but response remains open for further handling
2025-06-21T20:39:48.984+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-3] org.springframework.web.HttpLogging      : [f4d1f00] Encoding [com.example.chatassistant.dto.ChatRequest@18ae141]
2025-06-21T20:39:48.992+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions       : [f4d1f00] [dd05e113-1] Response 200 OK
2025-06-21T20:39:48.993+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-3] org.springframework.web.HttpLogging      : [f4d1f00] [dd05e113-1] Decoded "event: error"
2025-06-21T20:39:48.993+08:00 DEBUG 97771 --- [chat-assistant] [         task-4] org.springframework.web.HttpLogging      : [f4d1f00] [dd05e113-1] Decoded "data: {"code":514,"statusText":"unAuthApiKey","message":"common:code_error.error_message.514","data" (truncated)..."
2025-06-21T20:39:48.994+08:00 DEBUG 97771 --- [chat-assistant] [         task-5] org.springframework.web.HttpLogging      : [f4d1f00] [dd05e113-1] Decoded ""
2025-06-21T20:39:48.994+08:00 DEBUG 97771 --- [chat-assistant] [         task-6] o.s.w.c.request.async.WebAsyncManager    : Async result set, dispatch to /api/chat/completions
2025-06-21T20:39:48.995+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-8] o.s.web.servlet.DispatcherServlet        : "ASYNC" dispatch for POST "/api/chat/completions", parameters={}
2025-06-21T20:39:48.995+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-8] s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-06-21T20:39:48.995+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-06-21T20:39:48.995+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Nothing to write: null body
2025-06-21T20:39:48.995+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-8] o.s.web.servlet.DispatcherServlet        : Exiting from "ASYNC" dispatch, status 200
2025-06-21T20:42:07.386+08:00 DEBUG 97771 --- [chat-assistant] [io-8080-exec-10] o.s.web.servlet.DispatcherServlet        : POST "/api/chat/completions", parameters={}
2025-06-21T20:42:07.389+08:00 DEBUG 97771 --- [chat-assistant] [io-8080-exec-10] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#streamChat(ChatRequest)
2025-06-21T20:42:07.390+08:00 DEBUG 97771 --- [chat-assistant] [io-8080-exec-10] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [com.example.chatassistant.dto.ChatRequest@5aa9b88d]
2025-06-21T20:42:07.391+08:00 DEBUG 97771 --- [chat-assistant] [io-8080-exec-10] o.s.w.r.f.client.ExchangeFunctions       : [546e5da8] HTTP POST http://localhost:3000/api/v1/chat/completions
2025-06-21T20:42:07.391+08:00 DEBUG 97771 --- [chat-assistant] [io-8080-exec-10] o.s.w.c.request.async.WebAsyncManager    : Started async request
2025-06-21T20:42:07.391+08:00 DEBUG 97771 --- [chat-assistant] [io-8080-exec-10] o.s.web.servlet.DispatcherServlet        : Exiting but response remains open for further handling
2025-06-21T20:42:07.392+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-4] org.springframework.web.HttpLogging      : [546e5da8] Encoding [com.example.chatassistant.dto.ChatRequest@5aa9b88d]
2025-06-21T20:42:07.402+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-4] o.s.w.r.f.client.ExchangeFunctions       : [546e5da8] [c63b41d4-1] Response 200 OK
2025-06-21T20:42:07.403+08:00 DEBUG 97771 --- [chat-assistant] [ctor-http-nio-4] org.springframework.web.HttpLogging      : [546e5da8] [c63b41d4-1] Decoded "event: error"
2025-06-21T20:42:07.404+08:00 DEBUG 97771 --- [chat-assistant] [         task-7] org.springframework.web.HttpLogging      : [546e5da8] [c63b41d4-1] Decoded "data: {"code":514,"statusText":"unAuthApiKey","message":"common:code_error.error_message.514","data" (truncated)..."
2025-06-21T20:42:07.404+08:00 DEBUG 97771 --- [chat-assistant] [         task-8] org.springframework.web.HttpLogging      : [546e5da8] [c63b41d4-1] Decoded ""
2025-06-21T20:42:07.405+08:00 DEBUG 97771 --- [chat-assistant] [         task-9] o.s.w.c.request.async.WebAsyncManager    : Async result set, dispatch to /api/chat/completions
2025-06-21T20:42:07.405+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : "ASYNC" dispatch for POST "/api/chat/completions", parameters={}
2025-06-21T20:42:07.406+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-06-21T20:42:07.406+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-06-21T20:42:07.406+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] m.m.a.RequestResponseBodyMethodProcessor : Nothing to write: null body
2025-06-21T20:42:07.406+08:00 DEBUG 97771 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Exiting from "ASYNC" dispatch, status 200
