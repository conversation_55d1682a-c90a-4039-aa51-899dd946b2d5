
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-06-21T15:09:13.729+08:00  INFO 8560 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Starting ChatAssistantApplication v1.0.0 using Java 17.0.3 with PID 8560 (/Users/<USER>/augment-projects/assistant_demo/backend/target/chat-assistant-1.0.0.jar started by zhi<PERSON><PERSON> in /Users/<USER>/augment-projects/assistant_demo/backend)
2025-06-21T15:09:13.731+08:00 DEBUG 8560 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-21T15:09:13.732+08:00  INFO 8560 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : No active profile set, falling back to 1 default profile: "default"
2025-06-21T15:09:14.293+08:00  INFO 8560 --- [chat-assistant] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-21T15:09:14.299+08:00  INFO 8560 --- [chat-assistant] [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-21T15:09:14.299+08:00  INFO 8560 --- [chat-assistant] [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-21T15:09:14.323+08:00  INFO 8560 --- [chat-assistant] [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-21T15:09:14.324+08:00  INFO 8560 --- [chat-assistant] [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 543 ms
2025-06-21T15:09:14.558+08:00 DEBUG 8560 --- [chat-assistant] [           main] s.w.s.m.m.a.RequestMappingHandlerMapping : 6 mappings in 'requestMappingHandlerMapping'
2025-06-21T15:09:14.573+08:00 DEBUG 8560 --- [chat-assistant] [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-21T15:09:14.628+08:00 DEBUG 8560 --- [chat-assistant] [           main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-21T15:09:14.644+08:00 DEBUG 8560 --- [chat-assistant] [           main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-21T15:09:14.684+08:00  WARN 8560 --- [chat-assistant] [           main] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-21T15:09:14.691+08:00  INFO 8560 --- [chat-assistant] [           main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-21T15:09:14.699+08:00 ERROR 8560 --- [chat-assistant] [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

