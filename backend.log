
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-06-21T20:44:59.182+08:00  INFO 11178 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Starting ChatAssistantApplication v1.0.0 using Java 17.0.3 with PID 11178 (/Users/<USER>/augment-projects/assistant_demo/backend/target/chat-assistant-1.0.0.jar started by zhi<PERSON><PERSON> in /Users/<USER>/augment-projects/assistant_demo/backend)
2025-06-21T20:44:59.183+08:00 DEBUG 11178 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-21T20:44:59.184+08:00  INFO 11178 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : No active profile set, falling back to 1 default profile: "default"
2025-06-21T20:44:59.713+08:00  INFO 11178 --- [chat-assistant] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8080 (http)
2025-06-21T20:44:59.719+08:00  INFO 11178 --- [chat-assistant] [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-21T20:44:59.719+08:00  INFO 11178 --- [chat-assistant] [           main] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-21T20:44:59.745+08:00  INFO 11178 --- [chat-assistant] [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-21T20:44:59.745+08:00  INFO 11178 --- [chat-assistant] [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 513 ms
2025-06-21T20:44:59.968+08:00 DEBUG 11178 --- [chat-assistant] [           main] s.w.s.m.m.a.RequestMappingHandlerMapping : 6 mappings in 'requestMappingHandlerMapping'
2025-06-21T20:44:59.982+08:00 DEBUG 11178 --- [chat-assistant] [           main] o.s.w.s.handler.SimpleUrlHandlerMapping  : Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-21T20:45:00.037+08:00 DEBUG 11178 --- [chat-assistant] [           main] s.w.s.m.m.a.RequestMappingHandlerAdapter : ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-21T20:45:00.051+08:00 DEBUG 11178 --- [chat-assistant] [           main] .m.m.a.ExceptionHandlerExceptionResolver : ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-21T20:45:00.093+08:00  INFO 11178 --- [chat-assistant] [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8080 (http) with context path ''
2025-06-21T20:45:00.101+08:00  INFO 11178 --- [chat-assistant] [           main] c.e.c.ChatAssistantApplication           : Started ChatAssistantApplication in 1.142 seconds (process running for 1.371)
2025-06-21T20:45:08.759+08:00  INFO 11178 --- [chat-assistant] [nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21T20:45:08.759+08:00  INFO 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-06-21T20:45:08.760+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected StandardServletMultipartResolver
2025-06-21T20:45:08.760+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected AcceptHeaderLocaleResolver
2025-06-21T20:45:08.760+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected FixedThemeResolver
2025-06-21T20:45:08.760+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@10231de5
2025-06-21T20:45:08.760+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Detected org.springframework.web.servlet.support.SessionFlashMapManager@3b97a22b
2025-06-21T20:45:08.760+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-21T20:45:08.761+08:00  INFO 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-06-21T20:45:08.764+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : GET "/api/health", parameters={}
2025-06-21T20:45:08.767+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#health()
2025-06-21T20:45:08.779+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Using 'application/json', given [*/*] and supported [application/json, application/*+json]
2025-06-21T20:45:08.780+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.w.s.m.m.a.HttpEntityMethodProcessor  : Writing [{service=chat-assistant, status=UP, timestamp=2025-06-21T12:45:08.772161Z}]
2025-06-21T20:45:08.788+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-06-21T20:45:38.551+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-3] o.s.web.servlet.DispatcherServlet        : POST "/api/chat/completions", parameters={}
2025-06-21T20:45:38.552+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-3] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#streamChat(ChatRequest)
2025-06-21T20:45:38.605+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-3] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [com.example.chatassistant.dto.ChatRequest@78fa47c2]
2025-06-21T20:45:38.639+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-3] o.s.w.r.f.client.ExchangeFunctions       : [6781a264] HTTP POST http://localhost:3000/api/v1/chat/completions
2025-06-21T20:45:38.676+08:00 ERROR 11178 --- [chat-assistant] [nio-8080-exec-3] i.n.r.d.DnsServerAddressStreamProviders  : Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
2025-06-21T20:45:38.709+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-3] o.s.w.c.request.async.WebAsyncManager    : Started async request
2025-06-21T20:45:38.710+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-3] o.s.web.servlet.DispatcherServlet        : Exiting but response remains open for further handling
2025-06-21T20:45:38.753+08:00 DEBUG 11178 --- [chat-assistant] [ctor-http-nio-2] org.springframework.web.HttpLogging      : [6781a264] Encoding [com.example.chatassistant.dto.ChatRequest@78fa47c2]
2025-06-21T20:45:38.772+08:00 DEBUG 11178 --- [chat-assistant] [ctor-http-nio-2] o.s.w.r.f.client.ExchangeFunctions       : [6781a264] [fe631436-1] Response 200 OK
2025-06-21T20:45:38.781+08:00 DEBUG 11178 --- [chat-assistant] [ctor-http-nio-2] org.springframework.web.HttpLogging      : [6781a264] [fe631436-1] Decoded "event: error"
2025-06-21T20:45:38.782+08:00 DEBUG 11178 --- [chat-assistant] [         task-1] org.springframework.web.HttpLogging      : [6781a264] [fe631436-1] Decoded "data: {"code":514,"statusText":"unAuthApiKey","message":"common:code_error.error_message.514","data" (truncated)..."
2025-06-21T20:45:38.783+08:00 DEBUG 11178 --- [chat-assistant] [         task-2] org.springframework.web.HttpLogging      : [6781a264] [fe631436-1] Decoded ""
2025-06-21T20:45:38.784+08:00 DEBUG 11178 --- [chat-assistant] [         task-3] o.s.w.c.request.async.WebAsyncManager    : Async result set, dispatch to /api/chat/completions
2025-06-21T20:45:38.787+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-4] o.s.web.servlet.DispatcherServlet        : "ASYNC" dispatch for POST "/api/chat/completions", parameters={}
2025-06-21T20:45:38.787+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-4] s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-06-21T20:45:38.788+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-06-21T20:45:38.788+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-4] m.m.a.RequestResponseBodyMethodProcessor : Nothing to write: null body
2025-06-21T20:45:38.789+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-4] o.s.web.servlet.DispatcherServlet        : Exiting from "ASYNC" dispatch, status 200
2025-06-21T21:09:05.420+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-6] o.s.web.servlet.DispatcherServlet        : OPTIONS "/api/chat/completions", parameters={}
2025-06-21T21:09:05.433+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-6] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#streamChat(ChatRequest)
2025-06-21T21:09:05.440+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-6] o.s.web.servlet.DispatcherServlet        : Completed 200 OK
2025-06-21T21:09:05.456+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-7] o.s.web.servlet.DispatcherServlet        : POST "/api/chat/completions", parameters={}
2025-06-21T21:09:05.456+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-7] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped to com.example.chatassistant.controller.ChatController#streamChat(ChatRequest)
2025-06-21T21:09:05.459+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-7] m.m.a.RequestResponseBodyMethodProcessor : Read "application/json;charset=UTF-8" to [com.example.chatassistant.dto.ChatRequest@18ae141]
2025-06-21T21:09:05.462+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-7] o.s.w.r.f.client.ExchangeFunctions       : [f4d1f00] HTTP POST http://localhost:3000/api/v1/chat/completions
2025-06-21T21:09:05.467+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-7] o.s.w.c.request.async.WebAsyncManager    : Started async request
2025-06-21T21:09:05.467+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-7] o.s.web.servlet.DispatcherServlet        : Exiting but response remains open for further handling
2025-06-21T21:09:05.470+08:00 DEBUG 11178 --- [chat-assistant] [ctor-http-nio-3] org.springframework.web.HttpLogging      : [f4d1f00] Encoding [com.example.chatassistant.dto.ChatRequest@18ae141]
2025-06-21T21:09:05.488+08:00 DEBUG 11178 --- [chat-assistant] [ctor-http-nio-3] o.s.w.r.f.client.ExchangeFunctions       : [f4d1f00] [9faaae81-1] Response 200 OK
2025-06-21T21:09:05.489+08:00 DEBUG 11178 --- [chat-assistant] [ctor-http-nio-3] org.springframework.web.HttpLogging      : [f4d1f00] [9faaae81-1] Decoded "event: error"
2025-06-21T21:09:05.491+08:00 DEBUG 11178 --- [chat-assistant] [         task-4] org.springframework.web.HttpLogging      : [f4d1f00] [9faaae81-1] Decoded "data: {"code":514,"statusText":"unAuthApiKey","message":"common:code_error.error_message.514","data" (truncated)..."
2025-06-21T21:09:05.492+08:00 DEBUG 11178 --- [chat-assistant] [         task-5] org.springframework.web.HttpLogging      : [f4d1f00] [9faaae81-1] Decoded ""
2025-06-21T21:09:05.492+08:00 DEBUG 11178 --- [chat-assistant] [         task-6] o.s.w.c.request.async.WebAsyncManager    : Async result set, dispatch to /api/chat/completions
2025-06-21T21:09:05.493+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-8] o.s.web.servlet.DispatcherServlet        : "ASYNC" dispatch for POST "/api/chat/completions", parameters={}
2025-06-21T21:09:05.493+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-8] s.w.s.m.m.a.RequestMappingHandlerAdapter : Resume with async result []
2025-06-21T21:09:05.494+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Found 'Content-Type:text/event-stream' in response
2025-06-21T21:09:05.494+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-8] m.m.a.RequestResponseBodyMethodProcessor : Nothing to write: null body
2025-06-21T21:09:05.494+08:00 DEBUG 11178 --- [chat-assistant] [nio-8080-exec-8] o.s.web.servlet.DispatcherServlet        : Exiting from "ASYNC" dispatch, status 200
