#!/bin/bash

# 智能聊天助手启动脚本

echo "🚀 启动智能聊天助手项目..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境，请安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到Maven环境，请安装Maven 3.6或更高版本"
    exit 1
fi

# 检查Python环境（用于启动前端服务器）
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "⚠️  警告: 未找到Python环境，将无法自动启动前端演示服务器"
    PYTHON_AVAILABLE=false
else
    PYTHON_AVAILABLE=true
fi

echo "✅ 环境检查完成"

# 构建并启动后端服务
echo "📦 构建后端服务..."
cd backend

# 检查是否已经构建过
if [ ! -f "target/chat-assistant-1.0.0.jar" ]; then
    echo "🔨 首次运行，正在编译项目..."
    mvn clean package -DskipTests
    if [ $? -ne 0 ]; then
        echo "❌ 后端构建失败"
        exit 1
    fi
else
    echo "📦 使用已存在的构建文件"
fi

echo "🚀 启动后端服务..."
# 在后台启动Spring Boot应用
nohup java -jar target/chat-assistant-1.0.0.jar > ../backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端服务启动
echo "⏳ 等待后端服务启动..."
sleep 10

# 检查后端服务是否启动成功
if curl -s http://localhost:8080/api/health > /dev/null; then
    echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"
    echo "📍 后端服务地址: http://localhost:8080"
else
    echo "❌ 后端服务启动失败，请检查日志: backend.log"
    exit 1
fi

cd ..

# 启动前端演示服务器
if [ "$PYTHON_AVAILABLE" = true ]; then
    echo "🌐 启动前端演示服务器..."
    cd frontend
    
    # 检查端口3000是否被占用
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  端口3000已被占用，尝试使用端口3001..."
        FRONTEND_PORT=3001
    else
        FRONTEND_PORT=3000
    fi
    
    # 启动Python HTTP服务器（在frontend目录中）
    if command -v python3 &> /dev/null; then
        nohup python3 -m http.server $FRONTEND_PORT --directory ./ > ../frontend.log 2>&1 &
    else
        nohup python -m SimpleHTTPServer $FRONTEND_PORT --directory ./ > ../frontend.log 2>&1 &
    fi

    FRONTEND_PID=$!

    # 不要立即切换目录，让HTTP服务器在frontend目录中运行
    
    # 等待前端服务启动
    sleep 3
    
    if curl -s http://localhost:$FRONTEND_PORT > /dev/null; then
        echo "✅ 前端演示服务器启动成功 (PID: $FRONTEND_PID)"
        echo "📍 演示页面地址: http://localhost:$FRONTEND_PORT"
    else
        echo "❌ 前端服务器启动失败，请检查日志: frontend.log"
    fi
    
    cd ..
else
    echo "⚠️  跳过前端服务器启动，请手动启动HTTP服务器"
    echo "💡 建议命令: cd frontend && python3 -m http.server 3000"
fi

# 保存进程ID到文件
echo $BACKEND_PID > .backend.pid
if [ ! -z "$FRONTEND_PID" ]; then
    echo $FRONTEND_PID > .frontend.pid
fi

echo ""
echo "🎉 项目启动完成！"
echo ""
echo "📋 服务信息:"
echo "   后端API服务: http://localhost:8080"
echo "   健康检查: http://localhost:8080/api/health"
if [ ! -z "$FRONTEND_PID" ]; then
    echo "   演示页面: http://localhost:$FRONTEND_PORT"
fi
echo ""
echo "📝 日志文件:"
echo "   后端日志: backend.log"
if [ ! -z "$FRONTEND_PID" ]; then
    echo "   前端日志: frontend.log"
fi
echo ""
echo "🛑 停止服务: ./stop.sh"
echo ""
echo "💡 提示: 请确保您的AI模型服务正在运行，并在 backend/src/main/resources/application.yml 中配置正确的API地址和令牌"
